"""
Main application for Telegram Tip Extractor.
Orchestrates all components and handles the main application flow.
"""

import asyncio
import logging
import signal
import sys
from datetime import datetime
from typing import Dict, Any, Optional

from config_loader import Config<PERSON>oader, setup_logging
from telegram_client import TelegramTipClient
from parser import MessageParser, ChannelSpecificParser
from storage import <PERSON><PERSON><PERSON>ogger, ClickHouseStorage


class TelegramTipExtractor:
    """Main application class for Telegram Tip Extractor."""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        Initialize the application.
        
        Args:
            config_path: Path to configuration file
        """
        self.config_path = config_path
        self.config = None
        self.telegram_client = None
        self.message_parser = None
        self.csv_logger = None
        self.clickhouse_storage = None
        self.is_running = False
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logging.info(f"Received signal {signum}, shutting down gracefully...")
        self.is_running = False
    
    async def initialize(self) -> bool:
        """
        Initialize all components.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            # Load configuration
            config_loader = ConfigLoader(self.config_path)
            self.config = config_loader.load_config()
            
            # Setup logging
            setup_logging(config_loader.get_logging_config())
            logging.info("Starting Telegram Tip Extractor")
            
            # Initialize CSV logger
            file_paths = config_loader.get_file_paths()
            self.csv_logger = CSVLogger(
                file_paths['raw_csv_log'],
                file_paths['enriched_csv_log']
            )
            logging.info("CSV logger initialized")
            
            # Initialize message parser
            parsing_config = config_loader.get_parsing_config()
            self.message_parser = MessageParser(
                file_paths['stock_list'],
                parsing_config.get('fuzzy_match_threshold', 80),
                parsing_config.get('max_targets', 3)
            )
            logging.info("Message parser initialized")
            
            # Initialize Telegram client
            telegram_config = config_loader.get_telegram_config()
            self.telegram_client = TelegramTipClient(
                telegram_config['api_id'],
                telegram_config['api_hash'],
                telegram_config['session_name']
            )
            
            # Connect to Telegram
            if not await self.telegram_client.connect():
                logging.error("Failed to connect to Telegram")
                return False
            
            # Setup channels
            channels = config_loader.get_channels()
            if not await self.telegram_client.setup_channels(channels):
                logging.error("Failed to setup channels")
                return False
            
            # Initialize ClickHouse storage (optional)
            try:
                clickhouse_config = config_loader.get_clickhouse_config()
                self.clickhouse_storage = ClickHouseStorage(clickhouse_config)
                
                if self.clickhouse_storage.connect():
                    if self.clickhouse_storage.create_table():
                        logging.info("ClickHouse storage initialized")
                    else:
                        logging.warning("Failed to create ClickHouse table")
                        self.clickhouse_storage = None
                else:
                    logging.warning("Failed to connect to ClickHouse, continuing without database storage")
                    self.clickhouse_storage = None
            except Exception as e:
                logging.warning(f"ClickHouse initialization failed: {e}, continuing without database storage")
                self.clickhouse_storage = None
            
            # Set message handler
            self.telegram_client.set_message_handler(self._handle_message)
            
            logging.info("All components initialized successfully")
            return True
            
        except Exception as e:
            logging.error(f"Initialization failed: {e}")
            return False
    
    def _handle_message(self, message_data: Dict[str, Any]) -> None:
        """
        Handle incoming messages.
        
        Args:
            message_data: Raw message data from Telegram
        """
        try:
            # Log raw message
            self.csv_logger.log_raw_message(
                message_data['timestamp'],
                message_data['channel'],
                message_data['message_id'],
                message_data['raw_message']
            )
            
            # Parse and enrich message
            enriched_data = self.message_parser.parse_message(message_data)
            
            if enriched_data:
                # Log enriched message to CSV
                self.csv_logger.log_enriched_message(enriched_data)
                
                # Insert into ClickHouse if available
                if self.clickhouse_storage:
                    self.clickhouse_storage.insert_enriched_message(enriched_data)
                
                logging.info(f"Processed trading tip: {enriched_data['stock_symbol']} "
                           f"from {enriched_data['channel']}")
            else:
                logging.debug(f"Message from {message_data['channel']} was not a trading tip")
                
        except Exception as e:
            logging.error(f"Error handling message: {e}")
    
    async def run(self) -> None:
        """Run the main application loop."""
        if not await self.initialize():
            logging.error("Failed to initialize application")
            return
        
        try:
            self.is_running = True
            logging.info("Starting message monitoring...")
            
            # Start monitoring in the background
            monitor_task = asyncio.create_task(self.telegram_client.start_monitoring())
            
            # Main loop
            while self.is_running:
                await asyncio.sleep(1)
                
                # Print statistics every 5 minutes
                if datetime.now().minute % 5 == 0 and datetime.now().second == 0:
                    stats = self.message_parser.get_statistics()
                    logging.info(f"Parser statistics: {stats}")
            
            # Cancel monitoring task
            monitor_task.cancel()
            try:
                await monitor_task
            except asyncio.CancelledError:
                pass
            
        except Exception as e:
            logging.error(f"Error in main loop: {e}")
        finally:
            await self.cleanup()
    
    async def fetch_historical_messages(self, hours: int = 24) -> None:
        """
        Fetch and process historical messages.
        
        Args:
            hours: Number of hours to look back
        """
        if not self.telegram_client:
            logging.error("Telegram client not initialized")
            return
        
        logging.info(f"Fetching historical messages from last {hours} hours...")
        
        try:
            messages = await self.telegram_client.fetch_recent_messages(hours)
            
            for message_data in messages:
                self._handle_message(message_data)
            
            stats = self.message_parser.get_statistics()
            logging.info(f"Historical processing complete. Statistics: {stats}")
            
        except Exception as e:
            logging.error(f"Error fetching historical messages: {e}")
    
    async def get_channel_info(self) -> None:
        """Display information about monitored channels."""
        if not self.telegram_client:
            logging.error("Telegram client not initialized")
            return
        
        try:
            channels_info = await self.telegram_client.get_channel_info()
            
            print("\nMonitored Channels:")
            print("-" * 50)
            for info in channels_info:
                print(f"Title: {info['title']}")
                print(f"ID: {info['id']}")
                print(f"Type: {info['type']}")
                if info['username']:
                    print(f"Username: @{info['username']}")
                if info['participants_count']:
                    print(f"Participants: {info['participants_count']}")
                print("-" * 30)
                
        except Exception as e:
            logging.error(f"Error getting channel info: {e}")
    
    async def cleanup(self) -> None:
        """Cleanup resources."""
        logging.info("Cleaning up resources...")
        
        if self.telegram_client:
            await self.telegram_client.disconnect()
        
        if self.clickhouse_storage:
            self.clickhouse_storage.close()
        
        logging.info("Cleanup complete")


async def main():
    """Main entry point."""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "login":
            # Interactive login mode
            from telegram_client import interactive_login
            
            print("Interactive Telegram login")
            print("Get your API credentials from https://my.telegram.org")
            
            try:
                api_id = int(input("Enter API ID: "))
                api_hash = input("Enter API Hash: ")
                session_name = input("Enter session name (default: telegram_tip_extractor): ") or "telegram_tip_extractor"
                
                success = await interactive_login(api_id, api_hash, session_name)
                if success:
                    print("Login successful! You can now run the main application.")
                else:
                    print("Login failed. Please try again.")
            except ValueError:
                print("Invalid API ID. Please enter a valid number.")
            except KeyboardInterrupt:
                print("\nLogin cancelled.")
            return
        
        elif command == "historical":
            # Fetch historical messages
            hours = 24
            if len(sys.argv) > 2:
                try:
                    hours = int(sys.argv[2])
                except ValueError:
                    print("Invalid hours value. Using default 24 hours.")
            
            app = TelegramTipExtractor()
            await app.initialize()
            await app.fetch_historical_messages(hours)
            await app.cleanup()
            return
        
        elif command == "channels":
            # Show channel information
            app = TelegramTipExtractor()
            await app.initialize()
            await app.get_channel_info()
            await app.cleanup()
            return
        
        elif command == "help":
            print("Telegram Tip Extractor")
            print("Usage:")
            print("  python main.py                 - Run the main application")
            print("  python main.py login           - Interactive Telegram login")
            print("  python main.py historical [hours] - Fetch historical messages")
            print("  python main.py channels        - Show channel information")
            print("  python main.py help            - Show this help")
            return
    
    # Default: run main application
    app = TelegramTipExtractor()
    await app.run()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nApplication stopped by user")
    except Exception as e:
        logging.error(f"Application error: {e}")
        sys.exit(1)
