"""
Utility functions for Telegram Tip Extractor.
Includes fuzzy matching, text processing, and helper functions.
"""

import pandas as pd
import re
import logging
from typing import Dict, List, Optional, Tuple, Any
from rapidfuzz import fuzz, process
from datetime import datetime, timedelta
import os


logger = logging.getLogger(__name__)


class StockMatcher:
    """Handles fuzzy matching of stock symbols and names."""
    
    def __init__(self, stock_list_path: str, threshold: int = 80):
        """
        Initialize the stock matcher.
        
        Args:
            stock_list_path: Path to CSV file containing stock symbols and names
            threshold: Minimum similarity score for matching (0-100)
        """
        self.threshold = threshold
        self.stocks_df = None
        self.symbol_list = []
        self.name_list = []
        self.load_stock_list(stock_list_path)
    
    def load_stock_list(self, stock_list_path: str) -> None:
        """
        Load stock list from CSV file.
        
        Args:
            stock_list_path: Path to CSV file with columns: symbol, name, exchange
        """
        try:
            if not os.path.exists(stock_list_path):
                logger.warning(f"Stock list file not found: {stock_list_path}")
                self.stocks_df = pd.DataFrame(columns=['symbol', 'name', 'exchange'])
                return
                
            self.stocks_df = pd.read_csv(stock_list_path)
            
            # Validate required columns
            required_columns = ['symbol', 'name']
            for col in required_columns:
                if col not in self.stocks_df.columns:
                    raise ValueError(f"Missing required column in stock list: {col}")
            
            # Create lists for fuzzy matching
            self.symbol_list = self.stocks_df['symbol'].str.upper().tolist()
            self.name_list = self.stocks_df['name'].str.upper().tolist()
            
            logger.info(f"Loaded {len(self.stocks_df)} stocks from {stock_list_path}")
            
        except Exception as e:
            logger.error(f"Error loading stock list: {e}")
            self.stocks_df = pd.DataFrame(columns=['symbol', 'name', 'exchange'])
    
    def find_stock_symbol(self, text: str) -> Optional[str]:
        """
        Find the best matching stock symbol from text.
        
        Args:
            text: Input text to search for stock symbols
            
        Returns:
            Best matching stock symbol or None if no good match found
        """
        if not self.symbol_list:
            return None
            
        text_upper = text.upper()
        
        # First try exact symbol matching
        for symbol in self.symbol_list:
            if symbol in text_upper:
                return symbol
        
        # Try fuzzy matching on symbols
        symbol_match = process.extractOne(
            text_upper, 
            self.symbol_list, 
            scorer=fuzz.partial_ratio
        )
        
        if symbol_match and symbol_match[1] >= self.threshold:
            return symbol_match[0]
        
        # Try fuzzy matching on company names
        name_match = process.extractOne(
            text_upper, 
            self.name_list, 
            scorer=fuzz.partial_ratio
        )
        
        if name_match and name_match[1] >= self.threshold:
            # Find corresponding symbol
            name_index = self.name_list.index(name_match[0])
            return self.symbol_list[name_index]
        
        return None


class TextProcessor:
    """Handles text processing and extraction of trading information."""
    
    @staticmethod
    def extract_price(text: str, keywords: List[str] = None) -> Optional[float]:
        """
        Extract price from text based on keywords.
        
        Args:
            text: Input text
            keywords: List of keywords to look for before price
            
        Returns:
            Extracted price or None
        """
        if keywords is None:
            keywords = ['price', 'at', '@', 'entry', 'buy', 'sell']
        
        # Pattern to match price after keywords
        for keyword in keywords:
            pattern = rf'{keyword}\s*:?\s*(\d+(?:\.\d+)?)'
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    return float(match.group(1))
                except ValueError:
                    continue
        
        # Fallback: extract any number that looks like a price
        price_pattern = r'\b(\d+(?:\.\d+)?)\b'
        matches = re.findall(price_pattern, text)
        if matches:
            try:
                return float(matches[0])
            except ValueError:
                pass
        
        return None
    
    @staticmethod
    def extract_targets(text: str, max_targets: int = 3) -> List[float]:
        """
        Extract target prices from text.
        
        Args:
            text: Input text
            max_targets: Maximum number of targets to extract
            
        Returns:
            List of target prices
        """
        targets = []
        
        # Look for target patterns
        target_patterns = [
            r'target\s*(\d+)?\s*:?\s*([\d\s\,\.]+)',
            r'tgt\s*(\d+)?\s*:?\s*([\d\s\,\.]+)',
            r't(\d+)\s*:?\s*([\d\s\,\.]+)',
        ]

        for pattern in target_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                try:
                    # Extract all numbers from the target text
                    target_text = match.group(2) if len(match.groups()) >= 2 else match.group(1)
                    if target_text:
                        numbers = re.findall(r'\d+(?:\.\d+)?', target_text)
                        for num in numbers:
                            targets.append(float(num))
                except (ValueError, IndexError):
                    continue
        
        # Remove duplicates and sort
        targets = sorted(list(set(targets)))
        
        return targets[:max_targets]
    
    @staticmethod
    def extract_stop_loss(text: str) -> Optional[float]:
        """
        Extract stop loss from text.
        
        Args:
            text: Input text
            
        Returns:
            Stop loss price or None
        """
        sl_patterns = [
            r'stop\s*loss\s*:?\s*(\d+(?:\.\d+)?)',
            r'sl\s*:?\s*(\d+(?:\.\d+)?)',
            r'stop\s*:?\s*(\d+(?:\.\d+)?)',
        ]
        
        for pattern in sl_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    return float(match.group(1))
                except ValueError:
                    continue
        
        return None
    
    @staticmethod
    def extract_direction(text: str) -> Optional[str]:
        """
        Extract trading direction from text.
        
        Args:
            text: Input text
            
        Returns:
            Trading direction (Buy/Sell/Long/Short) or None
        """
        text_lower = text.lower()
        
        if any(word in text_lower for word in ['buy', 'long', 'call']):
            return 'Buy'
        elif any(word in text_lower for word in ['sell', 'short', 'put']):
            return 'Sell'
        
        return None
    
    @staticmethod
    def extract_option_info(text: str) -> Tuple[Optional[str], Optional[float], Optional[str]]:
        """
        Extract option information from text.
        
        Args:
            text: Input text
            
        Returns:
            Tuple of (option_type, strike_price, expiry_date)
        """
        option_type = None
        strike_price = None
        expiry_date = None
        
        # Extract option type (CE/PE)
        if re.search(r'\bce\b', text, re.IGNORECASE):
            option_type = 'CE'
        elif re.search(r'\bpe\b', text, re.IGNORECASE):
            option_type = 'PE'
        
        # Extract strike price
        strike_pattern = r'(\d+)\s*(ce|pe)'
        match = re.search(strike_pattern, text, re.IGNORECASE)
        if match:
            try:
                strike_price = float(match.group(1))
            except ValueError:
                pass
        
        # Extract expiry date (basic patterns)
        expiry_patterns = [
            r'(\d{1,2})\s*(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)',
            r'(\d{1,2})/(\d{1,2})/(\d{2,4})',
            r'(\d{1,2})-(\d{1,2})-(\d{2,4})',
        ]
        
        for pattern in expiry_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                expiry_date = match.group(0)
                break
        
        return option_type, strike_price, expiry_date
    
    @staticmethod
    def determine_instrument_type(text: str) -> str:
        """
        Determine instrument type from text.
        
        Args:
            text: Input text
            
        Returns:
            Instrument type (Equity/Futures/Options)
        """
        text_lower = text.lower()
        
        if any(word in text_lower for word in ['ce', 'pe', 'call', 'put', 'option']):
            return 'Options'
        elif any(word in text_lower for word in ['fut', 'future', 'futures']):
            return 'Futures'
        else:
            return 'Equity'


def clean_message_text(text: str) -> str:
    """
    Clean and normalize message text.
    
    Args:
        text: Raw message text
        
    Returns:
        Cleaned text
    """
    if not text:
        return ""
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Remove special characters but keep basic punctuation
    text = re.sub(r'[^\w\s\.\-\:\@\#\%\/]', ' ', text)
    
    return text


def parse_timeframe(text: str) -> Optional[str]:
    """
    Extract timeframe from text.
    
    Args:
        text: Input text
        
    Returns:
        Timeframe string or None
    """
    timeframe_patterns = [
        r'(\d+)\s*(min|minute|minutes)',
        r'(\d+)\s*(hour|hours|hr|hrs)',
        r'(\d+)\s*(day|days)',
        r'(\d+)\s*(week|weeks)',
        r'(intraday|swing|positional)',
    ]
    
    for pattern in timeframe_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            return match.group(0)
    
    return None
