"""
Storage module for Telegram Tip Extractor.
Handles CSV logging and ClickHouse database operations.
"""

import pandas as pd
import csv
import os
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
import clickhouse_connect
from pathlib import Path


logger = logging.getLogger(__name__)


class CSVLogger:
    """Handles CSV file operations for logging messages."""
    
    def __init__(self, raw_csv_path: str, enriched_csv_path: str):
        """
        Initialize CSV logger.
        
        Args:
            raw_csv_path: Path to raw messages CSV file
            enriched_csv_path: Path to enriched messages CSV file
        """
        self.raw_csv_path = raw_csv_path
        self.enriched_csv_path = enriched_csv_path
        
        # Create directories if they don't exist
        for path in [raw_csv_path, enriched_csv_path]:
            directory = os.path.dirname(path)
            if directory:
                Path(directory).mkdir(parents=True, exist_ok=True)
        
        # Initialize CSV files with headers if they don't exist
        self._initialize_csv_files()
    
    def _initialize_csv_files(self) -> None:
        """Initialize CSV files with headers if they don't exist."""
        # Raw messages CSV headers
        raw_headers = [
            'timestamp', 'channel', 'message_id', 'raw_message'
        ]
        
        # Enriched messages CSV headers
        enriched_headers = [
            'timestamp', 'channel', 'message_id', 'raw_message',
            'stock_symbol', 'instrument_type', 'strike_price', 'option_type',
            'expiry_date', 'direction', 'entry_price', 'stop_loss',
            'target1', 'target2', 'target3', 'timeframe'
        ]
        
        # Create raw CSV if it doesn't exist
        if not os.path.exists(self.raw_csv_path):
            with open(self.raw_csv_path, 'w', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow(raw_headers)
            logger.info(f"Created raw CSV file: {self.raw_csv_path}")
        
        # Create enriched CSV if it doesn't exist
        if not os.path.exists(self.enriched_csv_path):
            with open(self.enriched_csv_path, 'w', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow(enriched_headers)
            logger.info(f"Created enriched CSV file: {self.enriched_csv_path}")
    
    def log_raw_message(self, timestamp: datetime, channel: str, 
                       message_id: int, raw_message: str) -> None:
        """
        Log a raw message to CSV.
        
        Args:
            timestamp: Message timestamp
            channel: Channel name/ID
            message_id: Telegram message ID
            raw_message: Raw message text
        """
        try:
            with open(self.raw_csv_path, 'a', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow([
                    timestamp.isoformat(),
                    channel,
                    message_id,
                    raw_message
                ])
            logger.debug(f"Logged raw message from {channel}: {message_id}")
        except Exception as e:
            logger.error(f"Error logging raw message: {e}")
    
    def log_enriched_message(self, enriched_data: Dict[str, Any]) -> None:
        """
        Log an enriched message to CSV.
        
        Args:
            enriched_data: Dictionary containing enriched message data
        """
        try:
            # Ensure all required fields are present
            required_fields = [
                'timestamp', 'channel', 'message_id', 'raw_message',
                'stock_symbol', 'instrument_type', 'strike_price', 'option_type',
                'expiry_date', 'direction', 'entry_price', 'stop_loss',
                'target1', 'target2', 'target3', 'timeframe'
            ]
            
            row_data = []
            for field in required_fields:
                value = enriched_data.get(field, '')
                # Convert timestamp to ISO format if it's a datetime object
                if field == 'timestamp' and isinstance(value, datetime):
                    value = value.isoformat()
                row_data.append(value)
            
            with open(self.enriched_csv_path, 'a', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow(row_data)
            
            logger.debug(f"Logged enriched message: {enriched_data.get('stock_symbol', 'Unknown')}")
        except Exception as e:
            logger.error(f"Error logging enriched message: {e}")
    
    def get_recent_messages(self, hours: int = 24, enriched: bool = False) -> pd.DataFrame:
        """
        Get recent messages from CSV.
        
        Args:
            hours: Number of hours to look back
            enriched: Whether to get enriched messages (True) or raw messages (False)
            
        Returns:
            DataFrame containing recent messages
        """
        try:
            csv_path = self.enriched_csv_path if enriched else self.raw_csv_path
            
            if not os.path.exists(csv_path):
                return pd.DataFrame()
            
            df = pd.read_csv(csv_path)
            
            if df.empty:
                return df
            
            # Convert timestamp column to datetime
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # Filter by time
            cutoff_time = datetime.now() - pd.Timedelta(hours=hours)
            recent_df = df[df['timestamp'] >= cutoff_time]
            
            return recent_df.sort_values('timestamp', ascending=False)
        
        except Exception as e:
            logger.error(f"Error reading recent messages: {e}")
            return pd.DataFrame()


class ClickHouseStorage:
    """Handles ClickHouse database operations."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize ClickHouse storage.
        
        Args:
            config: ClickHouse configuration dictionary
        """
        self.config = config
        self.client = None
        self.table_name = config['table']
        self.database = config['database']
    
    def connect(self) -> bool:
        """
        Connect to ClickHouse database.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            self.client = clickhouse_connect.get_client(
                host=self.config['host'],
                port=self.config['port'],
                username=self.config.get('user', 'default'),
                password=self.config.get('password', ''),
                database=self.database
            )
            
            # Test connection
            result = self.client.query('SELECT 1')
            logger.info("Successfully connected to ClickHouse")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to ClickHouse: {e}")
            return False
    
    def create_table(self) -> bool:
        """
        Create the enriched messages table if it doesn't exist.
        
        Returns:
            True if table created/exists, False otherwise
        """
        if not self.client:
            logger.error("ClickHouse client not connected")
            return False
        
        try:
            create_table_sql = f"""
            CREATE TABLE IF NOT EXISTS {self.database}.{self.table_name} (
                timestamp DateTime,
                channel String,
                message_id UInt64,
                raw_message String,
                stock_symbol String,
                instrument_type String,
                strike_price Nullable(Float64),
                option_type String,
                expiry_date String,
                direction String,
                entry_price Nullable(Float64),
                stop_loss Nullable(Float64),
                target1 Nullable(Float64),
                target2 Nullable(Float64),
                target3 Nullable(Float64),
                timeframe String,
                created_at DateTime DEFAULT now()
            ) ENGINE = MergeTree()
            ORDER BY (timestamp, channel, message_id)
            """
            
            self.client.command(create_table_sql)
            logger.info(f"Table {self.table_name} created/verified")
            return True
            
        except Exception as e:
            logger.error(f"Error creating table: {e}")
            return False
    
    def insert_enriched_message(self, enriched_data: Dict[str, Any]) -> bool:
        """
        Insert an enriched message into ClickHouse.
        
        Args:
            enriched_data: Dictionary containing enriched message data
            
        Returns:
            True if insertion successful, False otherwise
        """
        if not self.client:
            logger.error("ClickHouse client not connected")
            return False
        
        try:
            # Prepare data for insertion
            data = {
                'timestamp': enriched_data.get('timestamp'),
                'channel': enriched_data.get('channel', ''),
                'message_id': enriched_data.get('message_id', 0),
                'raw_message': enriched_data.get('raw_message', ''),
                'stock_symbol': enriched_data.get('stock_symbol', ''),
                'instrument_type': enriched_data.get('instrument_type', ''),
                'strike_price': enriched_data.get('strike_price'),
                'option_type': enriched_data.get('option_type', ''),
                'expiry_date': enriched_data.get('expiry_date', ''),
                'direction': enriched_data.get('direction', ''),
                'entry_price': enriched_data.get('entry_price'),
                'stop_loss': enriched_data.get('stop_loss'),
                'target1': enriched_data.get('target1'),
                'target2': enriched_data.get('target2'),
                'target3': enriched_data.get('target3'),
                'timeframe': enriched_data.get('timeframe', '')
            }
            
            # Convert datetime to string if needed
            if isinstance(data['timestamp'], datetime):
                data['timestamp'] = data['timestamp'].strftime('%Y-%m-%d %H:%M:%S')

            # Remove created_at from data since it has a default value
            if 'created_at' in data:
                del data['created_at']

            self.client.insert(f"{self.database}.{self.table_name}", [data])
            logger.debug(f"Inserted enriched message into ClickHouse: {data['stock_symbol']}")
            return True
            
        except Exception as e:
            logger.error(f"Error inserting into ClickHouse: {e}")
            return False
    
    def insert_batch(self, enriched_data_list: List[Dict[str, Any]]) -> bool:
        """
        Insert multiple enriched messages into ClickHouse.
        
        Args:
            enriched_data_list: List of enriched message dictionaries
            
        Returns:
            True if insertion successful, False otherwise
        """
        if not self.client or not enriched_data_list:
            return False
        
        try:
            # Prepare batch data
            batch_data = []
            for enriched_data in enriched_data_list:
                data = {
                    'timestamp': enriched_data.get('timestamp'),
                    'channel': enriched_data.get('channel', ''),
                    'message_id': enriched_data.get('message_id', 0),
                    'raw_message': enriched_data.get('raw_message', ''),
                    'stock_symbol': enriched_data.get('stock_symbol', ''),
                    'instrument_type': enriched_data.get('instrument_type', ''),
                    'strike_price': enriched_data.get('strike_price'),
                    'option_type': enriched_data.get('option_type', ''),
                    'expiry_date': enriched_data.get('expiry_date', ''),
                    'direction': enriched_data.get('direction', ''),
                    'entry_price': enriched_data.get('entry_price'),
                    'stop_loss': enriched_data.get('stop_loss'),
                    'target1': enriched_data.get('target1'),
                    'target2': enriched_data.get('target2'),
                    'target3': enriched_data.get('target3'),
                    'timeframe': enriched_data.get('timeframe', '')
                }
                
                # Convert datetime to string if needed
                if isinstance(data['timestamp'], datetime):
                    data['timestamp'] = data['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
                
                batch_data.append(data)
            
            self.client.insert(f"{self.database}.{self.table_name}", batch_data)
            logger.info(f"Inserted {len(batch_data)} enriched messages into ClickHouse")
            return True
            
        except Exception as e:
            logger.error(f"Error inserting batch into ClickHouse: {e}")
            return False
    
    def close(self) -> None:
        """Close ClickHouse connection."""
        if self.client:
            self.client.close()
            logger.info("ClickHouse connection closed")
