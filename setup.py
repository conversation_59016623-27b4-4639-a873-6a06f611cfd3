#!/usr/bin/env python3
"""
Setup script for Telegram Tip Extractor.
Helps with initial setup and configuration.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {sys.version.split()[0]} detected")
    return True


def create_virtual_environment():
    """Create virtual environment if it doesn't exist."""
    venv_path = Path("venv")
    
    if venv_path.exists():
        print("✅ Virtual environment already exists")
        return True
    
    try:
        print("📦 Creating virtual environment...")
        subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
        print("✅ Virtual environment created")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to create virtual environment: {e}")
        return False


def install_dependencies():
    """Install Python dependencies."""
    try:
        print("📦 Installing dependencies...")
        
        # Determine pip path
        if os.name == 'nt':  # Windows
            pip_path = "venv/Scripts/pip"
        else:  # Unix/Linux/macOS
            pip_path = "venv/bin/pip"
        
        subprocess.run([pip_path, "install", "-r", "requirements.txt"], check=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def create_directories():
    """Create necessary directories."""
    directories = ["data", "logs", "examples"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")


def check_config_file():
    """Check if config file exists and is valid."""
    config_path = Path("config.yaml")
    
    if not config_path.exists():
        print("❌ config.yaml not found")
        print("📝 Please copy config.yaml.template to config.yaml and update with your settings")
        return False
    
    # Basic validation
    try:
        import yaml
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        required_sections = ['telegram', 'channels', 'files']
        missing_sections = [s for s in required_sections if s not in config]
        
        if missing_sections:
            print(f"❌ Missing config sections: {missing_sections}")
            return False
        
        # Check if API credentials are set
        telegram_config = config.get('telegram', {})
        if (telegram_config.get('api_id') == 'YOUR_API_ID' or 
            telegram_config.get('api_hash') == 'YOUR_API_HASH'):
            print("⚠️  Please update config.yaml with your Telegram API credentials")
            print("   Get them from https://my.telegram.org")
            return False
        
        print("✅ Configuration file looks good")
        return True
        
    except Exception as e:
        print(f"❌ Error reading config file: {e}")
        return False


def check_stock_list():
    """Check if stock list file exists."""
    stock_list_path = Path("data/stock_list.csv")
    
    if not stock_list_path.exists():
        print("❌ Stock list file not found: data/stock_list.csv")
        print("📝 Please create or update the stock list file")
        return False
    
    try:
        import pandas as pd
        df = pd.read_csv(stock_list_path)
        
        required_columns = ['symbol', 'name']
        missing_columns = [c for c in required_columns if c not in df.columns]
        
        if missing_columns:
            print(f"❌ Missing columns in stock list: {missing_columns}")
            return False
        
        print(f"✅ Stock list loaded: {len(df)} stocks")
        return True
        
    except Exception as e:
        print(f"❌ Error reading stock list: {e}")
        return False


def run_tests():
    """Run component tests."""
    try:
        print("🧪 Running component tests...")
        
        # Determine python path
        if os.name == 'nt':  # Windows
            python_path = "venv/Scripts/python"
        else:  # Unix/Linux/macOS
            python_path = "venv/bin/python"
        
        result = subprocess.run([python_path, "test_components.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ All tests passed")
            return True
        else:
            print("❌ Some tests failed")
            print(result.stdout)
            return False
            
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False


def print_next_steps():
    """Print next steps for the user."""
    print("\n" + "="*60)
    print("🎉 SETUP COMPLETE!")
    print("="*60)
    print("\nNext steps:")
    print("1. Update config.yaml with your Telegram API credentials")
    print("   - Get API credentials from https://my.telegram.org")
    print("   - Update the 'channels' list with channels you want to monitor")
    print()
    print("2. Login to Telegram (first time only):")
    print("   source venv/bin/activate && python main.py login")
    print()
    print("3. Test channel access:")
    print("   source venv/bin/activate && python main.py channels")
    print()
    print("4. Start monitoring:")
    print("   source venv/bin/activate && python main.py")
    print()
    print("5. Process historical messages (optional):")
    print("   source venv/bin/activate && python main.py historical 24")
    print()
    print("For help: python main.py help")
    print("="*60)


def main():
    """Main setup function."""
    print("🚀 Telegram Tip Extractor Setup")
    print("="*40)
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Create virtual environment
    if not create_virtual_environment():
        return 1
    
    # Install dependencies
    if not install_dependencies():
        return 1
    
    # Create directories
    create_directories()
    
    # Check configuration
    config_ok = check_config_file()
    
    # Check stock list
    stock_list_ok = check_stock_list()
    
    # Run tests if everything is configured
    if config_ok and stock_list_ok:
        tests_ok = run_tests()
    else:
        tests_ok = False
        print("⚠️  Skipping tests due to configuration issues")
    
    # Print next steps
    print_next_steps()
    
    if config_ok and stock_list_ok and tests_ok:
        print("✅ Setup completed successfully!")
        return 0
    else:
        print("⚠️  Setup completed with warnings. Please address the issues above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
