# Telegram Tip Extractor

A Python application that monitors Telegram channels for stock trading tips, extracts structured data, and stores them in CSV files and ClickHouse database.

## Features

- **Real-time monitoring** of multiple Telegram channels
- **Intelligent parsing** of trading messages with fuzzy stock symbol matching
- **Multi-format support** for different channel message formats
- **CSV logging** for both raw and enriched messages
- **ClickHouse integration** for scalable data storage
- **Configurable parsing** with customizable thresholds and rules
- **Historical message processing** for backfilling data

## Project Structure

```
TelegramTipExtractor/
├── main.py                 # Main application entry point
├── config_loader.py        # Configuration management
├── telegram_client.py      # Telegram API client
├── parser.py              # Message parsing and enrichment
├── storage.py             # CSV and ClickHouse storage
├── utils.py               # Utility functions and helpers
├── config.yaml            # Configuration file
├── requirements.txt       # Python dependencies
├── data/
│   ├── stock_list.csv     # Stock symbols and names
│   ├── raw_messages.csv   # Raw message log
│   └── enriched_messages.csv # Enriched message log
└── logs/
    └── app.log            # Application logs
```

## Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd TelegramTipExtractor
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Get Telegram API credentials:**
   - Visit https://my.telegram.org
   - Log in with your phone number
   - Go to "API Development Tools"
   - Create a new application to get `api_id` and `api_hash`

4. **Configure the application:**
   - Copy `config.yaml` and update with your credentials
   - Update the `channels` list with channels you want to monitor
   - Modify file paths and ClickHouse settings as needed

## Configuration

### Telegram API Setup

1. **Get API credentials** from https://my.telegram.org:
   - `api_id`: Your application's API ID (integer)
   - `api_hash`: Your application's API hash (string)

2. **Update config.yaml:**
   ```yaml
   telegram:
     api_id: YOUR_API_ID
     api_hash: "YOUR_API_HASH"
     session_name: "telegram_tip_extractor"
   ```

### Channel Configuration

Add channels to monitor in `config.yaml`:

```yaml
channels:
  - "@public_channel_username"    # Public channel
  - "channel_id_number"           # Private channel (numeric ID)
  - "@another_channel"
```

**Note:** For private channels, you must be a member of the channel.

### Stock List

Update `data/stock_list.csv` with the stocks you want to track:

```csv
symbol,name,exchange
RELIANCE,Reliance Industries Limited,NSE
TCS,Tata Consultancy Services Limited,NSE
HDFCBANK,HDFC Bank Limited,NSE
```

## Usage

### First-time Setup

1. **Login to Telegram:**
   ```bash
   python main.py login
   ```
   This creates a session file for authentication.

2. **Test channel access:**
   ```bash
   python main.py channels
   ```
   This shows information about configured channels.

### Running the Application

1. **Start real-time monitoring:**
   ```bash
   python main.py
   ```

2. **Process historical messages:**
   ```bash
   python main.py historical 24  # Last 24 hours
   ```

3. **Show help:**
   ```bash
   python main.py help
   ```

### Output Files

- **Raw messages:** `data/raw_messages.csv`
  - Contains all messages from monitored channels
  - Fields: timestamp, channel, message_id, raw_message

- **Enriched messages:** `data/enriched_messages.csv`
  - Contains parsed trading information
  - Fields: timestamp, channel, message_id, raw_message, stock_symbol, instrument_type, strike_price, option_type, expiry_date, direction, entry_price, stop_loss, target1, target2, target3, timeframe

## Message Parsing

The application extracts the following information from trading messages:

- **Stock Symbol:** Matched against the stock list using fuzzy matching
- **Instrument Type:** Equity, Futures, or Options
- **Direction:** Buy/Sell/Long/Short
- **Entry Price:** Entry price for the trade
- **Stop Loss:** Stop loss level
- **Targets:** Up to 3 target prices
- **Option Details:** Strike price, option type (CE/PE), expiry date
- **Timeframe:** Trading timeframe (intraday, swing, etc.)

### Example Message Formats

The parser handles various message formats:

```
RELIANCE BUY at 2500, Target: 2600, 2700, SL: 2400

TCS CALL 3200 CE
Entry: 45
Target: 55, 65
SL: 35

INFY Futures Long @ 1450
Targets: 1480, 1520
Stop: 1420
```

## ClickHouse Integration

### Setup ClickHouse

1. **Install ClickHouse** (optional):
   ```bash
   # Ubuntu/Debian
   sudo apt-get install clickhouse-server clickhouse-client
   
   # Start service
   sudo service clickhouse-server start
   ```

2. **Configure in config.yaml:**
   ```yaml
   clickhouse:
     host: "localhost"
     port: 8123
     user: "default"
     password: ""
     database: "trading_tips"
     table: "enriched_messages"
   ```

3. **Create database:**
   ```sql
   CREATE DATABASE trading_tips;
   ```

The application will automatically create the required table.

## Customization

### Adding Channel-Specific Parsers

You can add custom parsing rules for specific channels:

```python
from parser import ChannelSpecificParser

def custom_channel_parser(message_data):
    # Custom parsing logic for specific channel format
    # Return enriched data dictionary
    pass

# In main.py
channel_parser = ChannelSpecificParser(base_parser)
channel_parser.add_channel_rule("Channel Name", custom_channel_parser)
```

### Adjusting Parsing Parameters

Modify parsing behavior in `config.yaml`:

```yaml
parsing:
  fuzzy_match_threshold: 80  # Stock name matching sensitivity (0-100)
  max_targets: 3             # Maximum targets to extract
```

## Monitoring and Logs

- **Application logs:** `logs/app.log`
- **Console output:** Real-time status and statistics
- **Statistics:** Parser success rates and message counts

## Troubleshooting

### Common Issues

1. **"User not authorized" error:**
   - Run `python main.py login` first
   - Make sure you complete the phone verification

2. **"Failed to add channel" error:**
   - Check channel username/ID is correct
   - Ensure you're a member of private channels
   - Verify channel is accessible

3. **No messages being parsed:**
   - Check if messages contain trading keywords
   - Verify stock symbols are in the stock list
   - Adjust `fuzzy_match_threshold` in config

4. **ClickHouse connection failed:**
   - Verify ClickHouse is running
   - Check connection parameters in config
   - Application continues without ClickHouse if connection fails

### Performance

- **Memory usage:** ~50-100MB for typical usage
- **Message rate:** Handles ~20 messages/minute reliably
- **Storage:** CSV files grow ~1MB per 1000 messages

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

This tool is for educational and research purposes only. Always verify trading information independently before making investment decisions. The authors are not responsible for any financial losses incurred from using this tool.
