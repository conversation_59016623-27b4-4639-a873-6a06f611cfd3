2025-09-14 22:35:29,886 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:35:29,886 - storage - INFO - Created raw CSV file: data/raw_messages.csv
2025-09-14 22:35:29,886 - storage - INFO - Created enriched CSV file: data/enriched_messages.csv
2025-09-14 22:35:29,886 - root - INFO - CSV logger initialized
2025-09-14 22:35:29,891 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:35:29,891 - root - INFO - Message parser initialized
2025-09-14 22:35:29,892 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:35:30,056 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:35:30,993 - telegram_client - INFO - Connected to Tel<PERSON>ram as <PERSON><PERSON><PERSON><PERSON><PERSON> (@<PERSON><PERSON>kaexpert)
2025-09-14 22:35:31,145 - telegram_client - INFO - Added channel: channel (@channel_username1)
2025-09-14 22:35:31,318 - telegram_client - INFO - Added channel: channel (@channel_username2)
2025-09-14 22:35:31,506 - telegram_client - ERROR - Failed to add channel channel_id_number: No user has "channel_id_number" as username
2025-09-14 22:35:31,506 - telegram_client - INFO - Successfully setup 2 channels
2025-09-14 22:35:31,549 - storage - ERROR - Failed to connect to ClickHouse: :HTTPDriver for http://localhost:8123 returned response code 404)
 Code: 81. DB::Exception: Database trading_tips does not exist. (UNKNOWN_DATABASE) (version 24.6.1.3290 (official build))

2025-09-14 22:35:31,549 - root - WARNING - Failed to connect to ClickHouse, continuing without database storage
2025-09-14 22:35:31,550 - root - INFO - All components initialized successfully
2025-09-14 22:35:31,550 - root - INFO - Starting message monitoring...
2025-09-14 22:35:31,550 - telegram_client - INFO - Started monitoring channels for new messages
2025-09-14 22:36:24,906 - root - INFO - Received signal 2, shutting down gracefully...
2025-09-14 22:36:25,602 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:36:25,603 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:36:25,611 - root - INFO - Cleaning up resources...
2025-09-14 22:36:25,611 - telethon.network.mtprotosender - INFO - Not disconnecting (already have no connection)
2025-09-14 22:36:25,613 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:36:25,613 - root - INFO - Cleanup complete
2025-09-14 22:39:21,776 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:39:21,776 - root - INFO - CSV logger initialized
2025-09-14 22:39:21,778 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:39:21,778 - root - INFO - Message parser initialized
2025-09-14 22:39:21,779 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:39:21,929 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:39:22,839 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:39:23,001 - telegram_client - WARNING - Skipping non-channel entity: @RishabSP
2025-09-14 22:39:23,175 - telegram_client - INFO - Added channel: channel (@channel_username2)
2025-09-14 22:39:23,311 - telegram_client - ERROR - Failed to add channel channel_id_number: No user has "channel_id_number" as username
2025-09-14 22:39:23,311 - telegram_client - INFO - Successfully setup 1 channels
2025-09-14 22:39:23,343 - storage - ERROR - Failed to connect to ClickHouse: :HTTPDriver for http://localhost:8123 returned response code 404)
 Code: 81. DB::Exception: Database telegram does not exist. (UNKNOWN_DATABASE) (version 24.6.1.3290 (official build))

2025-09-14 22:39:23,343 - root - WARNING - Failed to connect to ClickHouse, continuing without database storage
2025-09-14 22:39:23,343 - root - INFO - All components initialized successfully
2025-09-14 22:39:23,343 - root - INFO - Fetching historical messages from last 1 hours...
2025-09-14 22:39:23,485 - telegram_client - ERROR - Error fetching messages from channel: can't compare offset-naive and offset-aware datetimes
2025-09-14 22:39:23,485 - telegram_client - INFO - Fetched total 0 recent messages
2025-09-14 22:39:23,485 - root - INFO - Historical processing complete. Statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 22:39:23,485 - root - INFO - Cleaning up resources...
2025-09-14 22:39:23,486 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:39:23,486 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:39:23,488 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:39:23,488 - root - INFO - Cleanup complete
2025-09-14 22:40:15,154 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:40:15,154 - root - INFO - CSV logger initialized
2025-09-14 22:40:15,156 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:40:15,156 - root - INFO - Message parser initialized
2025-09-14 22:40:15,157 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:40:15,282 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:40:15,996 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:40:16,146 - telegram_client - WARNING - Skipping non-channel entity: @RishabSP
2025-09-14 22:40:16,273 - telegram_client - INFO - Added channel: channel (@channel_username2)
2025-09-14 22:40:16,746 - telegram_client - ERROR - Failed to add channel -1003065061158: Cannot find any entity corresponding to "-1003065061158"
2025-09-14 22:40:16,746 - telegram_client - INFO - Successfully setup 1 channels
2025-09-14 22:40:16,768 - storage - ERROR - Failed to connect to ClickHouse: :HTTPDriver for http://localhost:8123 returned response code 404)
 Code: 81. DB::Exception: Database telegram does not exist. (UNKNOWN_DATABASE) (version 24.6.1.3290 (official build))

2025-09-14 22:40:16,769 - root - WARNING - Failed to connect to ClickHouse, continuing without database storage
2025-09-14 22:40:16,769 - root - INFO - All components initialized successfully
2025-09-14 22:40:16,769 - root - INFO - Fetching historical messages from last 1 hours...
2025-09-14 22:40:16,907 - telegram_client - ERROR - Error fetching messages from channel: can't compare offset-naive and offset-aware datetimes
2025-09-14 22:40:16,907 - telegram_client - INFO - Fetched total 0 recent messages
2025-09-14 22:40:16,907 - root - INFO - Historical processing complete. Statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 22:40:16,907 - root - INFO - Cleaning up resources...
2025-09-14 22:40:16,907 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:40:16,907 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:40:16,911 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:40:16,911 - root - INFO - Cleanup complete
2025-09-14 22:40:41,751 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:40:41,751 - root - INFO - CSV logger initialized
2025-09-14 22:40:41,753 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:40:41,753 - root - INFO - Message parser initialized
2025-09-14 22:40:41,754 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:40:41,875 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:40:42,592 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:40:42,703 - telegram_client - WARNING - Skipping non-channel entity: @RishabSP
2025-09-14 22:40:42,820 - telegram_client - INFO - Added channel: channel (@channel_username2)
2025-09-14 22:40:43,413 - telegram_client - ERROR - Failed to add channel -1003065061158: Cannot find any entity corresponding to "-1003065061158"
2025-09-14 22:40:43,413 - telegram_client - INFO - Successfully setup 1 channels
2025-09-14 22:40:43,434 - storage - ERROR - Failed to connect to ClickHouse: :HTTPDriver for http://localhost:8123 returned response code 404)
 Code: 81. DB::Exception: Database telegram does not exist. (UNKNOWN_DATABASE) (version 24.6.1.3290 (official build))

2025-09-14 22:40:43,434 - root - WARNING - Failed to connect to ClickHouse, continuing without database storage
2025-09-14 22:40:43,434 - root - INFO - All components initialized successfully
2025-09-14 22:40:43,434 - root - INFO - Fetching historical messages from last 1 hours...
2025-09-14 22:40:43,562 - telegram_client - ERROR - Error fetching messages from channel: can't compare offset-naive and offset-aware datetimes
2025-09-14 22:40:43,562 - telegram_client - INFO - Fetched total 0 recent messages
2025-09-14 22:40:43,562 - root - INFO - Historical processing complete. Statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 22:40:43,562 - root - INFO - Cleaning up resources...
2025-09-14 22:40:43,562 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:40:43,562 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:40:43,564 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:40:43,565 - root - INFO - Cleanup complete
2025-09-14 22:42:05,531 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:42:05,531 - root - INFO - CSV logger initialized
2025-09-14 22:42:05,533 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:42:05,533 - root - INFO - Message parser initialized
2025-09-14 22:42:05,534 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:42:05,665 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:42:06,571 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:42:06,720 - telegram_client - WARNING - Skipping non-channel entity: @RishabSP
2025-09-14 22:42:06,888 - telegram_client - INFO - Added channel: channel (@channel_username2)
2025-09-14 22:42:07,447 - telegram_client - ERROR - Failed to add channel -1003065061158: Cannot find any entity corresponding to "-1003065061158"
2025-09-14 22:42:07,447 - telegram_client - INFO - Successfully setup 1 channels
2025-09-14 22:42:07,470 - storage - ERROR - Failed to connect to ClickHouse: :HTTPDriver for http://localhost:8123 returned response code 404)
 Code: 81. DB::Exception: Database telegram does not exist. (UNKNOWN_DATABASE) (version 24.6.1.3290 (official build))

2025-09-14 22:42:07,471 - root - WARNING - Failed to connect to ClickHouse, continuing without database storage
2025-09-14 22:42:07,471 - root - INFO - All components initialized successfully
2025-09-14 22:42:07,471 - root - INFO - Starting message monitoring...
2025-09-14 22:42:07,472 - telegram_client - INFO - Started monitoring channels for new messages
2025-09-14 22:45:00,642 - root - INFO - Parser statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 22:48:32,131 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:48:32,261 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:48:33,119 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:48:54,082 - telegram_client - INFO - Discovered 123 channels/groups
2025-09-14 22:48:54,101 - root - INFO - Cleaning up resources...
2025-09-14 22:48:54,102 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:48:54,102 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:48:54,116 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:48:54,116 - root - INFO - Cleanup complete
2025-09-14 22:49:34,869 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:49:34,869 - root - INFO - CSV logger initialized
2025-09-14 22:49:34,871 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:49:34,871 - root - INFO - Message parser initialized
2025-09-14 22:49:34,872 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:49:34,987 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:49:35,847 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:49:36,008 - telegram_client - INFO - Added channel: @ONE_CALL💎 (@one_call_new)
2025-09-14 22:49:36,185 - telegram_client - INFO - Added channel: Chart Studies with Safe Traders (@safetraders90)
2025-09-14 22:49:36,352 - telegram_client - INFO - Added channel: DAILY TRADING STRATEGIES (@rahuloptions)
2025-09-14 22:49:36,517 - telegram_client - INFO - Added channel: DAY TRADER - Educational Purpose (@daytraderstoption)
2025-09-14 22:49:37,154 - telegram_client - ERROR - Failed to add channel 1365133276: Cannot find any entity corresponding to "1365133276"
2025-09-14 22:49:37,154 - telegram_client - INFO - Successfully setup 4 channels
2025-09-14 22:49:37,182 - storage - INFO - Successfully connected to ClickHouse
2025-09-14 22:49:37,188 - storage - INFO - Table enriched_messages created/verified
2025-09-14 22:49:37,188 - root - INFO - ClickHouse storage initialized
2025-09-14 22:49:37,188 - root - INFO - All components initialized successfully
2025-09-14 22:49:37,188 - root - INFO - Fetching historical messages from last 2 hours...
2025-09-14 22:49:37,395 - telegram_client - ERROR - Error fetching messages from @ONE_CALL💎: can't compare offset-naive and offset-aware datetimes
2025-09-14 22:49:37,805 - telegram_client - ERROR - Error fetching messages from Chart Studies with Safe Traders: can't compare offset-naive and offset-aware datetimes
2025-09-14 22:49:38,058 - telegram_client - ERROR - Error fetching messages from DAILY TRADING STRATEGIES: can't compare offset-naive and offset-aware datetimes
2025-09-14 22:49:38,293 - telegram_client - ERROR - Error fetching messages from DAY TRADER - Educational Purpose: can't compare offset-naive and offset-aware datetimes
2025-09-14 22:49:38,293 - telegram_client - INFO - Fetched total 0 recent messages
2025-09-14 22:49:38,293 - root - INFO - Historical processing complete. Statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 22:49:38,293 - root - INFO - Cleaning up resources...
2025-09-14 22:49:38,293 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:49:38,294 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:49:38,296 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:49:38,296 - storage - INFO - ClickHouse connection closed
2025-09-14 22:49:38,296 - root - INFO - Cleanup complete
2025-09-14 22:50:00,938 - root - INFO - Parser statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 22:50:25,744 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:50:25,745 - root - INFO - CSV logger initialized
2025-09-14 22:50:25,746 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:50:25,747 - root - INFO - Message parser initialized
2025-09-14 22:50:25,747 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:50:25,873 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:50:26,671 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:50:26,813 - telegram_client - INFO - Added channel: @ONE_CALL💎 (@one_call_new)
2025-09-14 22:50:26,957 - telegram_client - INFO - Added channel: Chart Studies with Safe Traders (@safetraders90)
2025-09-14 22:50:27,100 - telegram_client - INFO - Added channel: DAILY TRADING STRATEGIES (@rahuloptions)
2025-09-14 22:50:27,235 - telegram_client - INFO - Added channel: DAY TRADER - Educational Purpose (@daytraderstoption)
2025-09-14 22:50:27,235 - telegram_client - INFO - Successfully setup 4 channels
2025-09-14 22:50:27,270 - storage - INFO - Successfully connected to ClickHouse
2025-09-14 22:50:27,273 - storage - INFO - Table enriched_messages created/verified
2025-09-14 22:50:27,273 - root - INFO - ClickHouse storage initialized
2025-09-14 22:50:27,273 - root - INFO - All components initialized successfully
2025-09-14 22:50:27,273 - root - INFO - Fetching historical messages from last 6 hours...
2025-09-14 22:50:27,454 - telegram_client - INFO - Fetched 0 recent messages from @ONE_CALL💎
2025-09-14 22:50:28,678 - telegram_client - INFO - Fetched 0 recent messages from Chart Studies with Safe Traders
2025-09-14 22:50:29,925 - telegram_client - INFO - Fetched 0 recent messages from DAILY TRADING STRATEGIES
2025-09-14 22:50:31,140 - telegram_client - INFO - Fetched 0 recent messages from DAY TRADER - Educational Purpose
2025-09-14 22:50:32,141 - telegram_client - INFO - Fetched total 0 recent messages
2025-09-14 22:50:32,142 - root - INFO - Historical processing complete. Statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 22:50:32,142 - root - INFO - Cleaning up resources...
2025-09-14 22:50:32,142 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:50:32,143 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:50:32,147 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:50:32,147 - storage - INFO - ClickHouse connection closed
2025-09-14 22:50:32,147 - root - INFO - Cleanup complete
2025-09-14 22:50:41,722 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:50:41,722 - root - INFO - CSV logger initialized
2025-09-14 22:50:41,724 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:50:41,724 - root - INFO - Message parser initialized
2025-09-14 22:50:41,725 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:50:41,879 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:50:42,793 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:50:42,941 - telegram_client - INFO - Added channel: @ONE_CALL💎 (@one_call_new)
2025-09-14 22:50:43,092 - telegram_client - INFO - Added channel: Chart Studies with Safe Traders (@safetraders90)
2025-09-14 22:50:43,245 - telegram_client - INFO - Added channel: DAILY TRADING STRATEGIES (@rahuloptions)
2025-09-14 22:50:43,400 - telegram_client - INFO - Added channel: DAY TRADER - Educational Purpose (@daytraderstoption)
2025-09-14 22:50:43,401 - telegram_client - INFO - Successfully setup 4 channels
2025-09-14 22:50:43,432 - storage - INFO - Successfully connected to ClickHouse
2025-09-14 22:50:43,436 - storage - INFO - Table enriched_messages created/verified
2025-09-14 22:50:43,436 - root - INFO - ClickHouse storage initialized
2025-09-14 22:50:43,436 - root - INFO - All components initialized successfully
2025-09-14 22:50:43,436 - root - INFO - Fetching historical messages from last 24 hours...
2025-09-14 22:50:43,656 - telegram_client - INFO - Fetched 0 recent messages from @ONE_CALL💎
2025-09-14 22:50:44,896 - telegram_client - INFO - Fetched 0 recent messages from Chart Studies with Safe Traders
2025-09-14 22:50:46,149 - telegram_client - INFO - Fetched 0 recent messages from DAILY TRADING STRATEGIES
2025-09-14 22:50:47,411 - telegram_client - INFO - Fetched 0 recent messages from DAY TRADER - Educational Purpose
2025-09-14 22:50:48,411 - telegram_client - INFO - Fetched total 0 recent messages
2025-09-14 22:50:48,411 - root - INFO - Historical processing complete. Statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 22:50:48,411 - root - INFO - Cleaning up resources...
2025-09-14 22:50:48,412 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:50:48,412 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:50:48,415 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:50:48,415 - storage - INFO - ClickHouse connection closed
2025-09-14 22:50:48,415 - root - INFO - Cleanup complete
2025-09-14 22:51:09,426 - root - INFO - Received signal 2, shutting down gracefully...
2025-09-14 22:51:10,011 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:51:10,012 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:51:10,014 - root - INFO - Cleaning up resources...
2025-09-14 22:51:10,016 - telethon.network.mtprotosender - INFO - Not disconnecting (already have no connection)
2025-09-14 22:51:10,018 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:51:10,018 - root - INFO - Cleanup complete
2025-09-14 22:51:13,658 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:51:13,658 - root - INFO - CSV logger initialized
2025-09-14 22:51:13,661 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:51:13,661 - root - INFO - Message parser initialized
2025-09-14 22:51:13,663 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:51:13,827 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:51:14,809 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:51:14,964 - telegram_client - INFO - Added channel: @ONE_CALL💎 (@one_call_new)
2025-09-14 22:51:15,126 - telegram_client - INFO - Added channel: Chart Studies with Safe Traders (@safetraders90)
2025-09-14 22:51:15,302 - telegram_client - INFO - Added channel: DAILY TRADING STRATEGIES (@rahuloptions)
2025-09-14 22:51:15,474 - telegram_client - INFO - Added channel: DAY TRADER - Educational Purpose (@daytraderstoption)
2025-09-14 22:51:15,474 - telegram_client - INFO - Successfully setup 4 channels
2025-09-14 22:51:15,530 - storage - INFO - Successfully connected to ClickHouse
2025-09-14 22:51:15,536 - storage - INFO - Table enriched_messages created/verified
2025-09-14 22:51:15,536 - root - INFO - ClickHouse storage initialized
2025-09-14 22:51:15,536 - root - INFO - All components initialized successfully
2025-09-14 22:51:15,536 - root - INFO - Starting message monitoring...
2025-09-14 22:51:15,536 - telegram_client - INFO - Started monitoring channels for new messages
2025-09-14 22:51:18,331 - root - INFO - Received signal 2, shutting down gracefully...
2025-09-14 22:51:18,544 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:51:18,544 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:51:18,548 - root - INFO - Cleaning up resources...
2025-09-14 22:51:18,548 - telethon.network.mtprotosender - INFO - Not disconnecting (already have no connection)
2025-09-14 22:51:18,551 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:51:18,551 - storage - INFO - ClickHouse connection closed
2025-09-14 22:51:18,551 - root - INFO - Cleanup complete
2025-09-14 22:52:05,751 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:52:05,752 - root - INFO - CSV logger initialized
2025-09-14 22:52:05,753 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:52:05,754 - root - INFO - Message parser initialized
2025-09-14 22:52:05,754 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:52:05,907 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:52:06,813 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:52:06,961 - telegram_client - INFO - Added channel: @ONE_CALL💎 (@one_call_new)
2025-09-14 22:52:07,113 - telegram_client - INFO - Added channel: Chart Studies with Safe Traders (@safetraders90)
2025-09-14 22:52:07,274 - telegram_client - INFO - Added channel: DAILY TRADING STRATEGIES (@rahuloptions)
2025-09-14 22:52:07,410 - telegram_client - INFO - Added channel: DAY TRADER - Educational Purpose (@daytraderstoption)
2025-09-14 22:52:07,410 - telegram_client - INFO - Successfully setup 4 channels
2025-09-14 22:52:07,454 - storage - INFO - Successfully connected to ClickHouse
2025-09-14 22:52:07,457 - storage - INFO - Table enriched_messages created/verified
2025-09-14 22:52:07,457 - root - INFO - ClickHouse storage initialized
2025-09-14 22:52:07,457 - root - INFO - All components initialized successfully
2025-09-14 22:52:07,457 - root - INFO - Testing parser with latest 5 messages per channel...
2025-09-14 22:52:07,582 - root - INFO - Fetched 5 messages from @ONE_CALL💎
2025-09-14 22:52:08,770 - root - INFO - Fetched 5 messages from Chart Studies with Safe Traders
2025-09-14 22:52:09,963 - root - INFO - Fetched 3 messages from DAILY TRADING STRATEGIES
2025-09-14 22:52:11,166 - root - INFO - Fetched 2 messages from DAY TRADER - Educational Purpose
2025-09-14 22:52:12,167 - root - INFO - Processing 15 total messages...
2025-09-14 22:52:12,178 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:52:12,178 - root - INFO - ✅ Parsed trading tip: TITAN (None) from Chart Studies with Safe Traders
2025-09-14 22:52:12,182 - root - INFO - Parser test complete. Statistics: {'total_messages': 15, 'parsed_messages': 1, 'failed_messages': 0, 'success_rate': 6.67}
2025-09-14 22:52:12,182 - root - INFO - Cleaning up resources...
2025-09-14 22:52:12,182 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:52:12,183 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:52:12,186 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:52:12,187 - storage - INFO - ClickHouse connection closed
2025-09-14 22:52:12,187 - root - INFO - Cleanup complete
2025-09-14 22:53:07,553 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:53:07,553 - root - INFO - CSV logger initialized
2025-09-14 22:53:07,555 - utils - INFO - Loaded 33 stocks from data/stock_list.csv
2025-09-14 22:53:07,555 - root - INFO - Message parser initialized
2025-09-14 22:53:07,556 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:53:07,690 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:53:08,600 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:53:08,767 - telegram_client - INFO - Added channel: @ONE_CALL💎 (@one_call_new)
2025-09-14 22:53:08,943 - telegram_client - INFO - Added channel: Chart Studies with Safe Traders (@safetraders90)
2025-09-14 22:53:09,122 - telegram_client - INFO - Added channel: DAILY TRADING STRATEGIES (@rahuloptions)
2025-09-14 22:53:09,289 - telegram_client - INFO - Added channel: DAY TRADER - Educational Purpose (@daytraderstoption)
2025-09-14 22:53:09,289 - telegram_client - INFO - Successfully setup 4 channels
2025-09-14 22:53:09,328 - storage - INFO - Successfully connected to ClickHouse
2025-09-14 22:53:09,331 - storage - INFO - Table enriched_messages created/verified
2025-09-14 22:53:09,332 - root - INFO - ClickHouse storage initialized
2025-09-14 22:53:09,332 - root - INFO - All components initialized successfully
2025-09-14 22:53:09,332 - root - INFO - Testing parser with latest 10 messages per channel...
2025-09-14 22:53:09,506 - root - INFO - Fetched 10 messages from @ONE_CALL💎
2025-09-14 22:53:10,709 - root - INFO - Fetched 10 messages from Chart Studies with Safe Traders
2025-09-14 22:53:11,884 - root - INFO - Fetched 8 messages from DAILY TRADING STRATEGIES
2025-09-14 22:53:13,135 - root - INFO - Fetched 7 messages from DAY TRADER - Educational Purpose
2025-09-14 22:53:14,136 - root - INFO - Processing 35 total messages...
2025-09-14 22:53:14,144 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,144 - root - INFO - ✅ Parsed trading tip: BANKNIFTY (None) from @ONE_CALL💎
2025-09-14 22:53:14,148 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,149 - root - INFO - ✅ Parsed trading tip: BANKNIFTY (None) from @ONE_CALL💎
2025-09-14 22:53:14,152 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,152 - root - INFO - ✅ Parsed trading tip: BANKNIFTY (None) from @ONE_CALL💎
2025-09-14 22:53:14,157 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,157 - root - INFO - ✅ Parsed trading tip: NIFTY (None) from @ONE_CALL💎
2025-09-14 22:53:14,160 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,160 - root - INFO - ✅ Parsed trading tip: NIFTY (None) from @ONE_CALL💎
2025-09-14 22:53:14,163 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,163 - root - INFO - ✅ Parsed trading tip: BANKNIFTY (None) from @ONE_CALL💎
2025-09-14 22:53:14,166 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,166 - root - INFO - ✅ Parsed trading tip: BANKNIFTY (None) from @ONE_CALL💎
2025-09-14 22:53:14,169 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,169 - root - INFO - ✅ Parsed trading tip: NIFTY (None) from @ONE_CALL💎
2025-09-14 22:53:14,172 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,172 - root - INFO - ✅ Parsed trading tip: NIFTY (None) from @ONE_CALL💎
2025-09-14 22:53:14,175 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,175 - root - INFO - ✅ Parsed trading tip: NIFTY (None) from @ONE_CALL💎
2025-09-14 22:53:14,179 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,179 - root - INFO - ✅ Parsed trading tip: HEROMOTOCORP (None) from Chart Studies with Safe Traders
2025-09-14 22:53:14,187 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,187 - root - INFO - ✅ Parsed trading tip: ASIANPAINT (None) from Chart Studies with Safe Traders
2025-09-14 22:53:14,193 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,194 - root - INFO - ✅ Parsed trading tip: INFY (Buy) from DAY TRADER - Educational Purpose
2025-09-14 22:53:14,198 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,198 - root - INFO - ✅ Parsed trading tip: TCS (Buy) from DAY TRADER - Educational Purpose
2025-09-14 22:53:14,202 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,202 - root - INFO - ✅ Parsed trading tip: TCS (Buy) from DAY TRADER - Educational Purpose
2025-09-14 22:53:14,203 - root - INFO - Parser test complete. Statistics: {'total_messages': 35, 'parsed_messages': 15, 'failed_messages': 0, 'success_rate': 42.86}
2025-09-14 22:53:14,203 - root - INFO - Cleaning up resources...
2025-09-14 22:53:14,203 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:53:14,203 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:53:14,207 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:53:14,207 - storage - INFO - ClickHouse connection closed
2025-09-14 22:53:14,207 - root - INFO - Cleanup complete
2025-09-14 22:56:39,046 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:56:39,046 - root - INFO - CSV logger initialized
2025-09-14 22:56:39,048 - utils - INFO - Loaded 33 stocks from data/stock_list.csv
2025-09-14 22:56:39,048 - root - INFO - Message parser initialized
2025-09-14 22:56:39,049 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:56:39,220 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:56:40,169 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:56:40,747 - telegram_client - ERROR - Failed to add channel 3065061158: Cannot find any entity corresponding to "3065061158"
2025-09-14 22:56:40,748 - telegram_client - ERROR - No channels successfully added
2025-09-14 22:56:40,748 - root - ERROR - Failed to setup channels
2025-09-14 22:56:40,748 - root - INFO - Fetching historical messages from last 1 hours...
2025-09-14 22:56:40,748 - telegram_client - ERROR - Client not connected or no channels setup
2025-09-14 22:56:40,748 - root - INFO - Historical processing complete. Statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 22:56:40,748 - root - INFO - Cleaning up resources...
2025-09-14 22:56:40,748 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:56:40,749 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:56:40,752 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:56:40,752 - root - INFO - Cleanup complete
2025-09-14 22:57:05,139 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:57:05,139 - root - INFO - CSV logger initialized
2025-09-14 22:57:05,142 - utils - INFO - Loaded 33 stocks from data/stock_list.csv
2025-09-14 22:57:05,143 - root - INFO - Message parser initialized
2025-09-14 22:57:05,143 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:57:05,273 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:57:06,191 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:57:06,192 - telegram_client - ERROR - Failed to add channel Channel TEST for ScorePandit: Cannot find any entity corresponding to "Channel TEST for ScorePandit"
2025-09-14 22:57:06,192 - telegram_client - ERROR - No channels successfully added
2025-09-14 22:57:06,192 - root - ERROR - Failed to setup channels
2025-09-14 22:57:06,193 - root - INFO - Fetching historical messages from last 1 hours...
2025-09-14 22:57:06,193 - telegram_client - ERROR - Client not connected or no channels setup
2025-09-14 22:57:06,193 - root - INFO - Historical processing complete. Statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 22:57:06,193 - root - INFO - Cleaning up resources...
2025-09-14 22:57:06,193 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:57:06,193 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:57:06,196 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:57:06,197 - root - INFO - Cleanup complete
