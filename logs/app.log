2025-09-14 22:35:29,886 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:35:29,886 - storage - INFO - Created raw CSV file: data/raw_messages.csv
2025-09-14 22:35:29,886 - storage - INFO - Created enriched CSV file: data/enriched_messages.csv
2025-09-14 22:35:29,886 - root - INFO - CSV logger initialized
2025-09-14 22:35:29,891 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:35:29,891 - root - INFO - Message parser initialized
2025-09-14 22:35:29,892 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:35:30,056 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:35:30,993 - telegram_client - INFO - Connected to Tel<PERSON>ram as <PERSON><PERSON><PERSON><PERSON><PERSON> (@<PERSON><PERSON>kaexpert)
2025-09-14 22:35:31,145 - telegram_client - INFO - Added channel: channel (@channel_username1)
2025-09-14 22:35:31,318 - telegram_client - INFO - Added channel: channel (@channel_username2)
2025-09-14 22:35:31,506 - telegram_client - ERROR - Failed to add channel channel_id_number: No user has "channel_id_number" as username
2025-09-14 22:35:31,506 - telegram_client - INFO - Successfully setup 2 channels
2025-09-14 22:35:31,549 - storage - ERROR - Failed to connect to ClickHouse: :HTTPDriver for http://localhost:8123 returned response code 404)
 Code: 81. DB::Exception: Database trading_tips does not exist. (UNKNOWN_DATABASE) (version 24.6.1.3290 (official build))

2025-09-14 22:35:31,549 - root - WARNING - Failed to connect to ClickHouse, continuing without database storage
2025-09-14 22:35:31,550 - root - INFO - All components initialized successfully
2025-09-14 22:35:31,550 - root - INFO - Starting message monitoring...
2025-09-14 22:35:31,550 - telegram_client - INFO - Started monitoring channels for new messages
2025-09-14 22:36:24,906 - root - INFO - Received signal 2, shutting down gracefully...
2025-09-14 22:36:25,602 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:36:25,603 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:36:25,611 - root - INFO - Cleaning up resources...
2025-09-14 22:36:25,611 - telethon.network.mtprotosender - INFO - Not disconnecting (already have no connection)
2025-09-14 22:36:25,613 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:36:25,613 - root - INFO - Cleanup complete
2025-09-14 22:39:21,776 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:39:21,776 - root - INFO - CSV logger initialized
2025-09-14 22:39:21,778 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:39:21,778 - root - INFO - Message parser initialized
2025-09-14 22:39:21,779 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:39:21,929 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:39:22,839 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:39:23,001 - telegram_client - WARNING - Skipping non-channel entity: @RishabSP
2025-09-14 22:39:23,175 - telegram_client - INFO - Added channel: channel (@channel_username2)
2025-09-14 22:39:23,311 - telegram_client - ERROR - Failed to add channel channel_id_number: No user has "channel_id_number" as username
2025-09-14 22:39:23,311 - telegram_client - INFO - Successfully setup 1 channels
2025-09-14 22:39:23,343 - storage - ERROR - Failed to connect to ClickHouse: :HTTPDriver for http://localhost:8123 returned response code 404)
 Code: 81. DB::Exception: Database telegram does not exist. (UNKNOWN_DATABASE) (version 24.6.1.3290 (official build))

2025-09-14 22:39:23,343 - root - WARNING - Failed to connect to ClickHouse, continuing without database storage
2025-09-14 22:39:23,343 - root - INFO - All components initialized successfully
2025-09-14 22:39:23,343 - root - INFO - Fetching historical messages from last 1 hours...
2025-09-14 22:39:23,485 - telegram_client - ERROR - Error fetching messages from channel: can't compare offset-naive and offset-aware datetimes
2025-09-14 22:39:23,485 - telegram_client - INFO - Fetched total 0 recent messages
2025-09-14 22:39:23,485 - root - INFO - Historical processing complete. Statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 22:39:23,485 - root - INFO - Cleaning up resources...
2025-09-14 22:39:23,486 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:39:23,486 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:39:23,488 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:39:23,488 - root - INFO - Cleanup complete
2025-09-14 22:40:15,154 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:40:15,154 - root - INFO - CSV logger initialized
2025-09-14 22:40:15,156 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:40:15,156 - root - INFO - Message parser initialized
2025-09-14 22:40:15,157 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:40:15,282 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:40:15,996 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:40:16,146 - telegram_client - WARNING - Skipping non-channel entity: @RishabSP
2025-09-14 22:40:16,273 - telegram_client - INFO - Added channel: channel (@channel_username2)
2025-09-14 22:40:16,746 - telegram_client - ERROR - Failed to add channel -1003065061158: Cannot find any entity corresponding to "-1003065061158"
2025-09-14 22:40:16,746 - telegram_client - INFO - Successfully setup 1 channels
2025-09-14 22:40:16,768 - storage - ERROR - Failed to connect to ClickHouse: :HTTPDriver for http://localhost:8123 returned response code 404)
 Code: 81. DB::Exception: Database telegram does not exist. (UNKNOWN_DATABASE) (version 24.6.1.3290 (official build))

2025-09-14 22:40:16,769 - root - WARNING - Failed to connect to ClickHouse, continuing without database storage
2025-09-14 22:40:16,769 - root - INFO - All components initialized successfully
2025-09-14 22:40:16,769 - root - INFO - Fetching historical messages from last 1 hours...
2025-09-14 22:40:16,907 - telegram_client - ERROR - Error fetching messages from channel: can't compare offset-naive and offset-aware datetimes
2025-09-14 22:40:16,907 - telegram_client - INFO - Fetched total 0 recent messages
2025-09-14 22:40:16,907 - root - INFO - Historical processing complete. Statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 22:40:16,907 - root - INFO - Cleaning up resources...
2025-09-14 22:40:16,907 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:40:16,907 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:40:16,911 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:40:16,911 - root - INFO - Cleanup complete
2025-09-14 22:40:41,751 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:40:41,751 - root - INFO - CSV logger initialized
2025-09-14 22:40:41,753 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:40:41,753 - root - INFO - Message parser initialized
2025-09-14 22:40:41,754 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:40:41,875 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:40:42,592 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:40:42,703 - telegram_client - WARNING - Skipping non-channel entity: @RishabSP
2025-09-14 22:40:42,820 - telegram_client - INFO - Added channel: channel (@channel_username2)
2025-09-14 22:40:43,413 - telegram_client - ERROR - Failed to add channel -1003065061158: Cannot find any entity corresponding to "-1003065061158"
2025-09-14 22:40:43,413 - telegram_client - INFO - Successfully setup 1 channels
2025-09-14 22:40:43,434 - storage - ERROR - Failed to connect to ClickHouse: :HTTPDriver for http://localhost:8123 returned response code 404)
 Code: 81. DB::Exception: Database telegram does not exist. (UNKNOWN_DATABASE) (version 24.6.1.3290 (official build))

2025-09-14 22:40:43,434 - root - WARNING - Failed to connect to ClickHouse, continuing without database storage
2025-09-14 22:40:43,434 - root - INFO - All components initialized successfully
2025-09-14 22:40:43,434 - root - INFO - Fetching historical messages from last 1 hours...
2025-09-14 22:40:43,562 - telegram_client - ERROR - Error fetching messages from channel: can't compare offset-naive and offset-aware datetimes
2025-09-14 22:40:43,562 - telegram_client - INFO - Fetched total 0 recent messages
2025-09-14 22:40:43,562 - root - INFO - Historical processing complete. Statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 22:40:43,562 - root - INFO - Cleaning up resources...
2025-09-14 22:40:43,562 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:40:43,562 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:40:43,564 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:40:43,565 - root - INFO - Cleanup complete
2025-09-14 22:42:05,531 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:42:05,531 - root - INFO - CSV logger initialized
2025-09-14 22:42:05,533 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:42:05,533 - root - INFO - Message parser initialized
2025-09-14 22:42:05,534 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:42:05,665 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:42:06,571 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:42:06,720 - telegram_client - WARNING - Skipping non-channel entity: @RishabSP
2025-09-14 22:42:06,888 - telegram_client - INFO - Added channel: channel (@channel_username2)
2025-09-14 22:42:07,447 - telegram_client - ERROR - Failed to add channel -1003065061158: Cannot find any entity corresponding to "-1003065061158"
2025-09-14 22:42:07,447 - telegram_client - INFO - Successfully setup 1 channels
2025-09-14 22:42:07,470 - storage - ERROR - Failed to connect to ClickHouse: :HTTPDriver for http://localhost:8123 returned response code 404)
 Code: 81. DB::Exception: Database telegram does not exist. (UNKNOWN_DATABASE) (version 24.6.1.3290 (official build))

2025-09-14 22:42:07,471 - root - WARNING - Failed to connect to ClickHouse, continuing without database storage
2025-09-14 22:42:07,471 - root - INFO - All components initialized successfully
2025-09-14 22:42:07,471 - root - INFO - Starting message monitoring...
2025-09-14 22:42:07,472 - telegram_client - INFO - Started monitoring channels for new messages
2025-09-14 22:45:00,642 - root - INFO - Parser statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
