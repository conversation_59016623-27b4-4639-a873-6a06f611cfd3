2025-09-14 22:35:29,886 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:35:29,886 - storage - INFO - Created raw CSV file: data/raw_messages.csv
2025-09-14 22:35:29,886 - storage - INFO - Created enriched CSV file: data/enriched_messages.csv
2025-09-14 22:35:29,886 - root - INFO - CSV logger initialized
2025-09-14 22:35:29,891 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:35:29,891 - root - INFO - Message parser initialized
2025-09-14 22:35:29,892 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:35:30,056 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:35:30,993 - telegram_client - INFO - Connected to Tel<PERSON>ram as <PERSON><PERSON><PERSON><PERSON><PERSON> (@<PERSON><PERSON>kaexpert)
2025-09-14 22:35:31,145 - telegram_client - INFO - Added channel: channel (@channel_username1)
2025-09-14 22:35:31,318 - telegram_client - INFO - Added channel: channel (@channel_username2)
2025-09-14 22:35:31,506 - telegram_client - ERROR - Failed to add channel channel_id_number: No user has "channel_id_number" as username
2025-09-14 22:35:31,506 - telegram_client - INFO - Successfully setup 2 channels
2025-09-14 22:35:31,549 - storage - ERROR - Failed to connect to ClickHouse: :HTTPDriver for http://localhost:8123 returned response code 404)
 Code: 81. DB::Exception: Database trading_tips does not exist. (UNKNOWN_DATABASE) (version 24.6.1.3290 (official build))

2025-09-14 22:35:31,549 - root - WARNING - Failed to connect to ClickHouse, continuing without database storage
2025-09-14 22:35:31,550 - root - INFO - All components initialized successfully
2025-09-14 22:35:31,550 - root - INFO - Starting message monitoring...
2025-09-14 22:35:31,550 - telegram_client - INFO - Started monitoring channels for new messages
2025-09-14 22:36:24,906 - root - INFO - Received signal 2, shutting down gracefully...
2025-09-14 22:36:25,602 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:36:25,603 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:36:25,611 - root - INFO - Cleaning up resources...
2025-09-14 22:36:25,611 - telethon.network.mtprotosender - INFO - Not disconnecting (already have no connection)
2025-09-14 22:36:25,613 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:36:25,613 - root - INFO - Cleanup complete
2025-09-14 22:39:21,776 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:39:21,776 - root - INFO - CSV logger initialized
2025-09-14 22:39:21,778 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:39:21,778 - root - INFO - Message parser initialized
2025-09-14 22:39:21,779 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:39:21,929 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:39:22,839 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:39:23,001 - telegram_client - WARNING - Skipping non-channel entity: @RishabSP
2025-09-14 22:39:23,175 - telegram_client - INFO - Added channel: channel (@channel_username2)
2025-09-14 22:39:23,311 - telegram_client - ERROR - Failed to add channel channel_id_number: No user has "channel_id_number" as username
2025-09-14 22:39:23,311 - telegram_client - INFO - Successfully setup 1 channels
2025-09-14 22:39:23,343 - storage - ERROR - Failed to connect to ClickHouse: :HTTPDriver for http://localhost:8123 returned response code 404)
 Code: 81. DB::Exception: Database telegram does not exist. (UNKNOWN_DATABASE) (version 24.6.1.3290 (official build))

2025-09-14 22:39:23,343 - root - WARNING - Failed to connect to ClickHouse, continuing without database storage
2025-09-14 22:39:23,343 - root - INFO - All components initialized successfully
2025-09-14 22:39:23,343 - root - INFO - Fetching historical messages from last 1 hours...
2025-09-14 22:39:23,485 - telegram_client - ERROR - Error fetching messages from channel: can't compare offset-naive and offset-aware datetimes
2025-09-14 22:39:23,485 - telegram_client - INFO - Fetched total 0 recent messages
2025-09-14 22:39:23,485 - root - INFO - Historical processing complete. Statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 22:39:23,485 - root - INFO - Cleaning up resources...
2025-09-14 22:39:23,486 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:39:23,486 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:39:23,488 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:39:23,488 - root - INFO - Cleanup complete
2025-09-14 22:40:15,154 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:40:15,154 - root - INFO - CSV logger initialized
2025-09-14 22:40:15,156 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:40:15,156 - root - INFO - Message parser initialized
2025-09-14 22:40:15,157 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:40:15,282 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:40:15,996 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:40:16,146 - telegram_client - WARNING - Skipping non-channel entity: @RishabSP
2025-09-14 22:40:16,273 - telegram_client - INFO - Added channel: channel (@channel_username2)
2025-09-14 22:40:16,746 - telegram_client - ERROR - Failed to add channel -1003065061158: Cannot find any entity corresponding to "-1003065061158"
2025-09-14 22:40:16,746 - telegram_client - INFO - Successfully setup 1 channels
2025-09-14 22:40:16,768 - storage - ERROR - Failed to connect to ClickHouse: :HTTPDriver for http://localhost:8123 returned response code 404)
 Code: 81. DB::Exception: Database telegram does not exist. (UNKNOWN_DATABASE) (version 24.6.1.3290 (official build))

2025-09-14 22:40:16,769 - root - WARNING - Failed to connect to ClickHouse, continuing without database storage
2025-09-14 22:40:16,769 - root - INFO - All components initialized successfully
2025-09-14 22:40:16,769 - root - INFO - Fetching historical messages from last 1 hours...
2025-09-14 22:40:16,907 - telegram_client - ERROR - Error fetching messages from channel: can't compare offset-naive and offset-aware datetimes
2025-09-14 22:40:16,907 - telegram_client - INFO - Fetched total 0 recent messages
2025-09-14 22:40:16,907 - root - INFO - Historical processing complete. Statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 22:40:16,907 - root - INFO - Cleaning up resources...
2025-09-14 22:40:16,907 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:40:16,907 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:40:16,911 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:40:16,911 - root - INFO - Cleanup complete
2025-09-14 22:40:41,751 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:40:41,751 - root - INFO - CSV logger initialized
2025-09-14 22:40:41,753 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:40:41,753 - root - INFO - Message parser initialized
2025-09-14 22:40:41,754 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:40:41,875 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:40:42,592 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:40:42,703 - telegram_client - WARNING - Skipping non-channel entity: @RishabSP
2025-09-14 22:40:42,820 - telegram_client - INFO - Added channel: channel (@channel_username2)
2025-09-14 22:40:43,413 - telegram_client - ERROR - Failed to add channel -1003065061158: Cannot find any entity corresponding to "-1003065061158"
2025-09-14 22:40:43,413 - telegram_client - INFO - Successfully setup 1 channels
2025-09-14 22:40:43,434 - storage - ERROR - Failed to connect to ClickHouse: :HTTPDriver for http://localhost:8123 returned response code 404)
 Code: 81. DB::Exception: Database telegram does not exist. (UNKNOWN_DATABASE) (version 24.6.1.3290 (official build))

2025-09-14 22:40:43,434 - root - WARNING - Failed to connect to ClickHouse, continuing without database storage
2025-09-14 22:40:43,434 - root - INFO - All components initialized successfully
2025-09-14 22:40:43,434 - root - INFO - Fetching historical messages from last 1 hours...
2025-09-14 22:40:43,562 - telegram_client - ERROR - Error fetching messages from channel: can't compare offset-naive and offset-aware datetimes
2025-09-14 22:40:43,562 - telegram_client - INFO - Fetched total 0 recent messages
2025-09-14 22:40:43,562 - root - INFO - Historical processing complete. Statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 22:40:43,562 - root - INFO - Cleaning up resources...
2025-09-14 22:40:43,562 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:40:43,562 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:40:43,564 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:40:43,565 - root - INFO - Cleanup complete
2025-09-14 22:42:05,531 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:42:05,531 - root - INFO - CSV logger initialized
2025-09-14 22:42:05,533 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:42:05,533 - root - INFO - Message parser initialized
2025-09-14 22:42:05,534 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:42:05,665 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:42:06,571 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:42:06,720 - telegram_client - WARNING - Skipping non-channel entity: @RishabSP
2025-09-14 22:42:06,888 - telegram_client - INFO - Added channel: channel (@channel_username2)
2025-09-14 22:42:07,447 - telegram_client - ERROR - Failed to add channel -1003065061158: Cannot find any entity corresponding to "-1003065061158"
2025-09-14 22:42:07,447 - telegram_client - INFO - Successfully setup 1 channels
2025-09-14 22:42:07,470 - storage - ERROR - Failed to connect to ClickHouse: :HTTPDriver for http://localhost:8123 returned response code 404)
 Code: 81. DB::Exception: Database telegram does not exist. (UNKNOWN_DATABASE) (version 24.6.1.3290 (official build))

2025-09-14 22:42:07,471 - root - WARNING - Failed to connect to ClickHouse, continuing without database storage
2025-09-14 22:42:07,471 - root - INFO - All components initialized successfully
2025-09-14 22:42:07,471 - root - INFO - Starting message monitoring...
2025-09-14 22:42:07,472 - telegram_client - INFO - Started monitoring channels for new messages
2025-09-14 22:45:00,642 - root - INFO - Parser statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 22:48:32,131 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:48:32,261 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:48:33,119 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:48:54,082 - telegram_client - INFO - Discovered 123 channels/groups
2025-09-14 22:48:54,101 - root - INFO - Cleaning up resources...
2025-09-14 22:48:54,102 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:48:54,102 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:48:54,116 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:48:54,116 - root - INFO - Cleanup complete
2025-09-14 22:49:34,869 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:49:34,869 - root - INFO - CSV logger initialized
2025-09-14 22:49:34,871 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:49:34,871 - root - INFO - Message parser initialized
2025-09-14 22:49:34,872 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:49:34,987 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:49:35,847 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:49:36,008 - telegram_client - INFO - Added channel: @ONE_CALL💎 (@one_call_new)
2025-09-14 22:49:36,185 - telegram_client - INFO - Added channel: Chart Studies with Safe Traders (@safetraders90)
2025-09-14 22:49:36,352 - telegram_client - INFO - Added channel: DAILY TRADING STRATEGIES (@rahuloptions)
2025-09-14 22:49:36,517 - telegram_client - INFO - Added channel: DAY TRADER - Educational Purpose (@daytraderstoption)
2025-09-14 22:49:37,154 - telegram_client - ERROR - Failed to add channel 1365133276: Cannot find any entity corresponding to "1365133276"
2025-09-14 22:49:37,154 - telegram_client - INFO - Successfully setup 4 channels
2025-09-14 22:49:37,182 - storage - INFO - Successfully connected to ClickHouse
2025-09-14 22:49:37,188 - storage - INFO - Table enriched_messages created/verified
2025-09-14 22:49:37,188 - root - INFO - ClickHouse storage initialized
2025-09-14 22:49:37,188 - root - INFO - All components initialized successfully
2025-09-14 22:49:37,188 - root - INFO - Fetching historical messages from last 2 hours...
2025-09-14 22:49:37,395 - telegram_client - ERROR - Error fetching messages from @ONE_CALL💎: can't compare offset-naive and offset-aware datetimes
2025-09-14 22:49:37,805 - telegram_client - ERROR - Error fetching messages from Chart Studies with Safe Traders: can't compare offset-naive and offset-aware datetimes
2025-09-14 22:49:38,058 - telegram_client - ERROR - Error fetching messages from DAILY TRADING STRATEGIES: can't compare offset-naive and offset-aware datetimes
2025-09-14 22:49:38,293 - telegram_client - ERROR - Error fetching messages from DAY TRADER - Educational Purpose: can't compare offset-naive and offset-aware datetimes
2025-09-14 22:49:38,293 - telegram_client - INFO - Fetched total 0 recent messages
2025-09-14 22:49:38,293 - root - INFO - Historical processing complete. Statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 22:49:38,293 - root - INFO - Cleaning up resources...
2025-09-14 22:49:38,293 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:49:38,294 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:49:38,296 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:49:38,296 - storage - INFO - ClickHouse connection closed
2025-09-14 22:49:38,296 - root - INFO - Cleanup complete
2025-09-14 22:50:00,938 - root - INFO - Parser statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 22:50:25,744 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:50:25,745 - root - INFO - CSV logger initialized
2025-09-14 22:50:25,746 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:50:25,747 - root - INFO - Message parser initialized
2025-09-14 22:50:25,747 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:50:25,873 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:50:26,671 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:50:26,813 - telegram_client - INFO - Added channel: @ONE_CALL💎 (@one_call_new)
2025-09-14 22:50:26,957 - telegram_client - INFO - Added channel: Chart Studies with Safe Traders (@safetraders90)
2025-09-14 22:50:27,100 - telegram_client - INFO - Added channel: DAILY TRADING STRATEGIES (@rahuloptions)
2025-09-14 22:50:27,235 - telegram_client - INFO - Added channel: DAY TRADER - Educational Purpose (@daytraderstoption)
2025-09-14 22:50:27,235 - telegram_client - INFO - Successfully setup 4 channels
2025-09-14 22:50:27,270 - storage - INFO - Successfully connected to ClickHouse
2025-09-14 22:50:27,273 - storage - INFO - Table enriched_messages created/verified
2025-09-14 22:50:27,273 - root - INFO - ClickHouse storage initialized
2025-09-14 22:50:27,273 - root - INFO - All components initialized successfully
2025-09-14 22:50:27,273 - root - INFO - Fetching historical messages from last 6 hours...
2025-09-14 22:50:27,454 - telegram_client - INFO - Fetched 0 recent messages from @ONE_CALL💎
2025-09-14 22:50:28,678 - telegram_client - INFO - Fetched 0 recent messages from Chart Studies with Safe Traders
2025-09-14 22:50:29,925 - telegram_client - INFO - Fetched 0 recent messages from DAILY TRADING STRATEGIES
2025-09-14 22:50:31,140 - telegram_client - INFO - Fetched 0 recent messages from DAY TRADER - Educational Purpose
2025-09-14 22:50:32,141 - telegram_client - INFO - Fetched total 0 recent messages
2025-09-14 22:50:32,142 - root - INFO - Historical processing complete. Statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 22:50:32,142 - root - INFO - Cleaning up resources...
2025-09-14 22:50:32,142 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:50:32,143 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:50:32,147 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:50:32,147 - storage - INFO - ClickHouse connection closed
2025-09-14 22:50:32,147 - root - INFO - Cleanup complete
2025-09-14 22:50:41,722 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:50:41,722 - root - INFO - CSV logger initialized
2025-09-14 22:50:41,724 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:50:41,724 - root - INFO - Message parser initialized
2025-09-14 22:50:41,725 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:50:41,879 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:50:42,793 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:50:42,941 - telegram_client - INFO - Added channel: @ONE_CALL💎 (@one_call_new)
2025-09-14 22:50:43,092 - telegram_client - INFO - Added channel: Chart Studies with Safe Traders (@safetraders90)
2025-09-14 22:50:43,245 - telegram_client - INFO - Added channel: DAILY TRADING STRATEGIES (@rahuloptions)
2025-09-14 22:50:43,400 - telegram_client - INFO - Added channel: DAY TRADER - Educational Purpose (@daytraderstoption)
2025-09-14 22:50:43,401 - telegram_client - INFO - Successfully setup 4 channels
2025-09-14 22:50:43,432 - storage - INFO - Successfully connected to ClickHouse
2025-09-14 22:50:43,436 - storage - INFO - Table enriched_messages created/verified
2025-09-14 22:50:43,436 - root - INFO - ClickHouse storage initialized
2025-09-14 22:50:43,436 - root - INFO - All components initialized successfully
2025-09-14 22:50:43,436 - root - INFO - Fetching historical messages from last 24 hours...
2025-09-14 22:50:43,656 - telegram_client - INFO - Fetched 0 recent messages from @ONE_CALL💎
2025-09-14 22:50:44,896 - telegram_client - INFO - Fetched 0 recent messages from Chart Studies with Safe Traders
2025-09-14 22:50:46,149 - telegram_client - INFO - Fetched 0 recent messages from DAILY TRADING STRATEGIES
2025-09-14 22:50:47,411 - telegram_client - INFO - Fetched 0 recent messages from DAY TRADER - Educational Purpose
2025-09-14 22:50:48,411 - telegram_client - INFO - Fetched total 0 recent messages
2025-09-14 22:50:48,411 - root - INFO - Historical processing complete. Statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 22:50:48,411 - root - INFO - Cleaning up resources...
2025-09-14 22:50:48,412 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:50:48,412 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:50:48,415 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:50:48,415 - storage - INFO - ClickHouse connection closed
2025-09-14 22:50:48,415 - root - INFO - Cleanup complete
2025-09-14 22:51:09,426 - root - INFO - Received signal 2, shutting down gracefully...
2025-09-14 22:51:10,011 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:51:10,012 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:51:10,014 - root - INFO - Cleaning up resources...
2025-09-14 22:51:10,016 - telethon.network.mtprotosender - INFO - Not disconnecting (already have no connection)
2025-09-14 22:51:10,018 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:51:10,018 - root - INFO - Cleanup complete
2025-09-14 22:51:13,658 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:51:13,658 - root - INFO - CSV logger initialized
2025-09-14 22:51:13,661 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:51:13,661 - root - INFO - Message parser initialized
2025-09-14 22:51:13,663 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:51:13,827 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:51:14,809 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:51:14,964 - telegram_client - INFO - Added channel: @ONE_CALL💎 (@one_call_new)
2025-09-14 22:51:15,126 - telegram_client - INFO - Added channel: Chart Studies with Safe Traders (@safetraders90)
2025-09-14 22:51:15,302 - telegram_client - INFO - Added channel: DAILY TRADING STRATEGIES (@rahuloptions)
2025-09-14 22:51:15,474 - telegram_client - INFO - Added channel: DAY TRADER - Educational Purpose (@daytraderstoption)
2025-09-14 22:51:15,474 - telegram_client - INFO - Successfully setup 4 channels
2025-09-14 22:51:15,530 - storage - INFO - Successfully connected to ClickHouse
2025-09-14 22:51:15,536 - storage - INFO - Table enriched_messages created/verified
2025-09-14 22:51:15,536 - root - INFO - ClickHouse storage initialized
2025-09-14 22:51:15,536 - root - INFO - All components initialized successfully
2025-09-14 22:51:15,536 - root - INFO - Starting message monitoring...
2025-09-14 22:51:15,536 - telegram_client - INFO - Started monitoring channels for new messages
2025-09-14 22:51:18,331 - root - INFO - Received signal 2, shutting down gracefully...
2025-09-14 22:51:18,544 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:51:18,544 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:51:18,548 - root - INFO - Cleaning up resources...
2025-09-14 22:51:18,548 - telethon.network.mtprotosender - INFO - Not disconnecting (already have no connection)
2025-09-14 22:51:18,551 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:51:18,551 - storage - INFO - ClickHouse connection closed
2025-09-14 22:51:18,551 - root - INFO - Cleanup complete
2025-09-14 22:52:05,751 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:52:05,752 - root - INFO - CSV logger initialized
2025-09-14 22:52:05,753 - utils - INFO - Loaded 30 stocks from data/stock_list.csv
2025-09-14 22:52:05,754 - root - INFO - Message parser initialized
2025-09-14 22:52:05,754 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:52:05,907 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:52:06,813 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:52:06,961 - telegram_client - INFO - Added channel: @ONE_CALL💎 (@one_call_new)
2025-09-14 22:52:07,113 - telegram_client - INFO - Added channel: Chart Studies with Safe Traders (@safetraders90)
2025-09-14 22:52:07,274 - telegram_client - INFO - Added channel: DAILY TRADING STRATEGIES (@rahuloptions)
2025-09-14 22:52:07,410 - telegram_client - INFO - Added channel: DAY TRADER - Educational Purpose (@daytraderstoption)
2025-09-14 22:52:07,410 - telegram_client - INFO - Successfully setup 4 channels
2025-09-14 22:52:07,454 - storage - INFO - Successfully connected to ClickHouse
2025-09-14 22:52:07,457 - storage - INFO - Table enriched_messages created/verified
2025-09-14 22:52:07,457 - root - INFO - ClickHouse storage initialized
2025-09-14 22:52:07,457 - root - INFO - All components initialized successfully
2025-09-14 22:52:07,457 - root - INFO - Testing parser with latest 5 messages per channel...
2025-09-14 22:52:07,582 - root - INFO - Fetched 5 messages from @ONE_CALL💎
2025-09-14 22:52:08,770 - root - INFO - Fetched 5 messages from Chart Studies with Safe Traders
2025-09-14 22:52:09,963 - root - INFO - Fetched 3 messages from DAILY TRADING STRATEGIES
2025-09-14 22:52:11,166 - root - INFO - Fetched 2 messages from DAY TRADER - Educational Purpose
2025-09-14 22:52:12,167 - root - INFO - Processing 15 total messages...
2025-09-14 22:52:12,178 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:52:12,178 - root - INFO - ✅ Parsed trading tip: TITAN (None) from Chart Studies with Safe Traders
2025-09-14 22:52:12,182 - root - INFO - Parser test complete. Statistics: {'total_messages': 15, 'parsed_messages': 1, 'failed_messages': 0, 'success_rate': 6.67}
2025-09-14 22:52:12,182 - root - INFO - Cleaning up resources...
2025-09-14 22:52:12,182 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:52:12,183 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:52:12,186 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:52:12,187 - storage - INFO - ClickHouse connection closed
2025-09-14 22:52:12,187 - root - INFO - Cleanup complete
2025-09-14 22:53:07,553 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:53:07,553 - root - INFO - CSV logger initialized
2025-09-14 22:53:07,555 - utils - INFO - Loaded 33 stocks from data/stock_list.csv
2025-09-14 22:53:07,555 - root - INFO - Message parser initialized
2025-09-14 22:53:07,556 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:53:07,690 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:53:08,600 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:53:08,767 - telegram_client - INFO - Added channel: @ONE_CALL💎 (@one_call_new)
2025-09-14 22:53:08,943 - telegram_client - INFO - Added channel: Chart Studies with Safe Traders (@safetraders90)
2025-09-14 22:53:09,122 - telegram_client - INFO - Added channel: DAILY TRADING STRATEGIES (@rahuloptions)
2025-09-14 22:53:09,289 - telegram_client - INFO - Added channel: DAY TRADER - Educational Purpose (@daytraderstoption)
2025-09-14 22:53:09,289 - telegram_client - INFO - Successfully setup 4 channels
2025-09-14 22:53:09,328 - storage - INFO - Successfully connected to ClickHouse
2025-09-14 22:53:09,331 - storage - INFO - Table enriched_messages created/verified
2025-09-14 22:53:09,332 - root - INFO - ClickHouse storage initialized
2025-09-14 22:53:09,332 - root - INFO - All components initialized successfully
2025-09-14 22:53:09,332 - root - INFO - Testing parser with latest 10 messages per channel...
2025-09-14 22:53:09,506 - root - INFO - Fetched 10 messages from @ONE_CALL💎
2025-09-14 22:53:10,709 - root - INFO - Fetched 10 messages from Chart Studies with Safe Traders
2025-09-14 22:53:11,884 - root - INFO - Fetched 8 messages from DAILY TRADING STRATEGIES
2025-09-14 22:53:13,135 - root - INFO - Fetched 7 messages from DAY TRADER - Educational Purpose
2025-09-14 22:53:14,136 - root - INFO - Processing 35 total messages...
2025-09-14 22:53:14,144 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,144 - root - INFO - ✅ Parsed trading tip: BANKNIFTY (None) from @ONE_CALL💎
2025-09-14 22:53:14,148 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,149 - root - INFO - ✅ Parsed trading tip: BANKNIFTY (None) from @ONE_CALL💎
2025-09-14 22:53:14,152 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,152 - root - INFO - ✅ Parsed trading tip: BANKNIFTY (None) from @ONE_CALL💎
2025-09-14 22:53:14,157 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,157 - root - INFO - ✅ Parsed trading tip: NIFTY (None) from @ONE_CALL💎
2025-09-14 22:53:14,160 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,160 - root - INFO - ✅ Parsed trading tip: NIFTY (None) from @ONE_CALL💎
2025-09-14 22:53:14,163 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,163 - root - INFO - ✅ Parsed trading tip: BANKNIFTY (None) from @ONE_CALL💎
2025-09-14 22:53:14,166 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,166 - root - INFO - ✅ Parsed trading tip: BANKNIFTY (None) from @ONE_CALL💎
2025-09-14 22:53:14,169 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,169 - root - INFO - ✅ Parsed trading tip: NIFTY (None) from @ONE_CALL💎
2025-09-14 22:53:14,172 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,172 - root - INFO - ✅ Parsed trading tip: NIFTY (None) from @ONE_CALL💎
2025-09-14 22:53:14,175 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,175 - root - INFO - ✅ Parsed trading tip: NIFTY (None) from @ONE_CALL💎
2025-09-14 22:53:14,179 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,179 - root - INFO - ✅ Parsed trading tip: HEROMOTOCORP (None) from Chart Studies with Safe Traders
2025-09-14 22:53:14,187 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,187 - root - INFO - ✅ Parsed trading tip: ASIANPAINT (None) from Chart Studies with Safe Traders
2025-09-14 22:53:14,193 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,194 - root - INFO - ✅ Parsed trading tip: INFY (Buy) from DAY TRADER - Educational Purpose
2025-09-14 22:53:14,198 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,198 - root - INFO - ✅ Parsed trading tip: TCS (Buy) from DAY TRADER - Educational Purpose
2025-09-14 22:53:14,202 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 22:53:14,202 - root - INFO - ✅ Parsed trading tip: TCS (Buy) from DAY TRADER - Educational Purpose
2025-09-14 22:53:14,203 - root - INFO - Parser test complete. Statistics: {'total_messages': 35, 'parsed_messages': 15, 'failed_messages': 0, 'success_rate': 42.86}
2025-09-14 22:53:14,203 - root - INFO - Cleaning up resources...
2025-09-14 22:53:14,203 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:53:14,203 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:53:14,207 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:53:14,207 - storage - INFO - ClickHouse connection closed
2025-09-14 22:53:14,207 - root - INFO - Cleanup complete
2025-09-14 22:56:39,046 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:56:39,046 - root - INFO - CSV logger initialized
2025-09-14 22:56:39,048 - utils - INFO - Loaded 33 stocks from data/stock_list.csv
2025-09-14 22:56:39,048 - root - INFO - Message parser initialized
2025-09-14 22:56:39,049 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:56:39,220 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:56:40,169 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:56:40,747 - telegram_client - ERROR - Failed to add channel 3065061158: Cannot find any entity corresponding to "3065061158"
2025-09-14 22:56:40,748 - telegram_client - ERROR - No channels successfully added
2025-09-14 22:56:40,748 - root - ERROR - Failed to setup channels
2025-09-14 22:56:40,748 - root - INFO - Fetching historical messages from last 1 hours...
2025-09-14 22:56:40,748 - telegram_client - ERROR - Client not connected or no channels setup
2025-09-14 22:56:40,748 - root - INFO - Historical processing complete. Statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 22:56:40,748 - root - INFO - Cleaning up resources...
2025-09-14 22:56:40,748 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:56:40,749 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:56:40,752 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:56:40,752 - root - INFO - Cleanup complete
2025-09-14 22:57:05,139 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 22:57:05,139 - root - INFO - CSV logger initialized
2025-09-14 22:57:05,142 - utils - INFO - Loaded 33 stocks from data/stock_list.csv
2025-09-14 22:57:05,143 - root - INFO - Message parser initialized
2025-09-14 22:57:05,143 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 22:57:05,273 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 22:57:06,191 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 22:57:06,192 - telegram_client - ERROR - Failed to add channel Channel TEST for ScorePandit: Cannot find any entity corresponding to "Channel TEST for ScorePandit"
2025-09-14 22:57:06,192 - telegram_client - ERROR - No channels successfully added
2025-09-14 22:57:06,192 - root - ERROR - Failed to setup channels
2025-09-14 22:57:06,193 - root - INFO - Fetching historical messages from last 1 hours...
2025-09-14 22:57:06,193 - telegram_client - ERROR - Client not connected or no channels setup
2025-09-14 22:57:06,193 - root - INFO - Historical processing complete. Statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 22:57:06,193 - root - INFO - Cleaning up resources...
2025-09-14 22:57:06,193 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 22:57:06,193 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 22:57:06,196 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 22:57:06,197 - root - INFO - Cleanup complete
2025-09-14 23:02:21,320 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 23:02:21,321 - root - INFO - CSV logger initialized
2025-09-14 23:02:21,326 - utils - INFO - Loaded 2142 stocks from data/stock_list.csv
2025-09-14 23:02:21,326 - root - INFO - Message parser initialized
2025-09-14 23:02:21,327 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 23:02:21,490 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 23:02:22,622 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 23:02:23,371 - telegram_client - INFO - Added channel: Channel TEST for ScorePandit (3065061158)
2025-09-14 23:02:23,372 - telegram_client - INFO - Successfully setup 1 channels
2025-09-14 23:02:23,410 - storage - INFO - Successfully connected to ClickHouse
2025-09-14 23:02:23,413 - storage - INFO - Table enriched_messages created/verified
2025-09-14 23:02:23,413 - root - INFO - ClickHouse storage initialized
2025-09-14 23:02:23,413 - root - INFO - All components initialized successfully
2025-09-14 23:02:23,413 - root - INFO - Testing parser with latest 5 messages per channel...
2025-09-14 23:02:23,547 - root - INFO - Fetched 4 messages from Channel TEST for ScorePandit
2025-09-14 23:02:24,548 - root - INFO - Processing 4 total messages...
2025-09-14 23:02:24,549 - root - INFO - Parser test complete. Statistics: {'total_messages': 4, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 23:02:24,549 - root - INFO - Cleaning up resources...
2025-09-14 23:02:24,550 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 23:02:24,550 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 23:02:24,554 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 23:02:24,555 - storage - INFO - ClickHouse connection closed
2025-09-14 23:02:24,555 - root - INFO - Cleanup complete
2025-09-14 23:03:03,215 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 23:03:03,216 - root - INFO - CSV logger initialized
2025-09-14 23:03:03,222 - utils - INFO - Loaded 2142 stocks from data/stock_list.csv
2025-09-14 23:03:03,222 - root - INFO - Message parser initialized
2025-09-14 23:03:03,223 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 23:03:03,357 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 23:03:04,362 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 23:03:05,237 - telegram_client - INFO - Added channel: Channel TEST for ScorePandit (3065061158)
2025-09-14 23:03:05,399 - telegram_client - INFO - Added channel: @ONE_CALL💎 (@one_call_new)
2025-09-14 23:03:05,399 - telegram_client - INFO - Successfully setup 2 channels
2025-09-14 23:03:05,423 - storage - INFO - Successfully connected to ClickHouse
2025-09-14 23:03:05,426 - storage - INFO - Table enriched_messages created/verified
2025-09-14 23:03:05,426 - root - INFO - ClickHouse storage initialized
2025-09-14 23:03:05,426 - root - INFO - All components initialized successfully
2025-09-14 23:03:05,426 - root - INFO - Testing parser with latest 3 messages per channel...
2025-09-14 23:03:05,599 - root - INFO - Fetched 3 messages from Channel TEST for ScorePandit
2025-09-14 23:03:06,760 - root - INFO - Fetched 3 messages from @ONE_CALL💎
2025-09-14 23:03:07,761 - root - INFO - Processing 6 total messages...
2025-09-14 23:03:07,771 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 23:03:07,773 - root - INFO - ✅ Parsed trading tip: NIFTY (None) from @ONE_CALL💎
2025-09-14 23:03:07,779 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 23:03:07,780 - root - INFO - ✅ Parsed trading tip: NIFTY (None) from @ONE_CALL💎
2025-09-14 23:03:07,785 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-14 23:03:07,787 - root - INFO - ✅ Parsed trading tip: NIFTY (None) from @ONE_CALL💎
2025-09-14 23:03:07,788 - root - INFO - Parser test complete. Statistics: {'total_messages': 6, 'parsed_messages': 3, 'failed_messages': 0, 'success_rate': 50.0}
2025-09-14 23:03:07,788 - root - INFO - Cleaning up resources...
2025-09-14 23:03:07,788 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 23:03:07,789 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 23:03:07,793 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 23:03:07,793 - storage - INFO - ClickHouse connection closed
2025-09-14 23:03:07,794 - root - INFO - Cleanup complete
2025-09-14 23:52:26,038 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 23:52:26,040 - root - INFO - CSV logger initialized
2025-09-14 23:52:26,044 - utils - INFO - Loaded 2142 stocks from data/stock_list.csv
2025-09-14 23:52:26,044 - root - INFO - Message parser initialized
2025-09-14 23:52:26,045 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 23:52:26,101 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 23:52:26,479 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 23:52:27,089 - telegram_client - INFO - Added channel: Channel TEST for ScorePandit (3065061158)
2025-09-14 23:52:27,090 - telegram_client - INFO - Successfully setup 1 channels
2025-09-14 23:52:27,130 - storage - INFO - Successfully connected to ClickHouse
2025-09-14 23:52:27,133 - storage - INFO - Table enriched_messages created/verified
2025-09-14 23:52:27,134 - root - INFO - ClickHouse storage initialized
2025-09-14 23:52:27,134 - root - INFO - All components initialized successfully
2025-09-14 23:52:27,134 - root - INFO - Fetching historical messages from last 1 hours...
2025-09-14 23:52:27,237 - telegram_client - INFO - Fetched 0 recent messages from Channel TEST for ScorePandit
2025-09-14 23:52:28,238 - telegram_client - INFO - Fetched total 0 recent messages
2025-09-14 23:52:28,239 - root - INFO - Historical processing complete. Statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 23:52:28,239 - root - INFO - Cleaning up resources...
2025-09-14 23:52:28,239 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 23:52:28,239 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 23:52:28,242 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 23:52:28,242 - storage - INFO - ClickHouse connection closed
2025-09-14 23:52:28,242 - root - INFO - Cleanup complete
2025-09-14 23:52:39,415 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 23:52:39,416 - root - INFO - CSV logger initialized
2025-09-14 23:52:39,420 - utils - INFO - Loaded 2142 stocks from data/stock_list.csv
2025-09-14 23:52:39,420 - root - INFO - Message parser initialized
2025-09-14 23:52:39,421 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 23:52:39,476 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 23:52:39,863 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 23:52:40,321 - telegram_client - INFO - Added channel: Channel TEST for ScorePandit (3065061158)
2025-09-14 23:52:40,321 - telegram_client - INFO - Successfully setup 1 channels
2025-09-14 23:52:40,353 - storage - INFO - Successfully connected to ClickHouse
2025-09-14 23:52:40,357 - storage - INFO - Table enriched_messages created/verified
2025-09-14 23:52:40,358 - root - INFO - ClickHouse storage initialized
2025-09-14 23:52:40,358 - root - INFO - All components initialized successfully
2025-09-14 23:52:40,358 - root - INFO - Fetching historical messages from last 5 hours...
2025-09-14 23:52:40,467 - telegram_client - INFO - Fetched 2 recent messages from Channel TEST for ScorePandit
2025-09-14 23:52:41,469 - telegram_client - INFO - Fetched total 2 recent messages
2025-09-14 23:52:41,470 - root - INFO - Historical processing complete. Statistics: {'total_messages': 2, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 23:52:41,470 - root - INFO - Cleaning up resources...
2025-09-14 23:52:41,470 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 23:52:41,471 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 23:52:41,475 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 23:52:41,475 - storage - INFO - ClickHouse connection closed
2025-09-14 23:52:41,475 - root - INFO - Cleanup complete
2025-09-14 23:54:44,562 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 23:54:44,563 - root - INFO - CSV logger initialized
2025-09-14 23:54:44,567 - utils - INFO - Loaded 2142 stocks from data/stock_list.csv
2025-09-14 23:54:44,567 - root - INFO - Message parser initialized
2025-09-14 23:54:44,568 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 23:54:44,636 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 23:54:45,100 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 23:54:45,583 - telegram_client - INFO - Added channel: Channel TEST for ScorePandit (3065061158)
2025-09-14 23:54:45,583 - telegram_client - INFO - Successfully setup 1 channels
2025-09-14 23:54:45,628 - storage - INFO - Successfully connected to ClickHouse
2025-09-14 23:54:45,630 - storage - INFO - Table enriched_messages created/verified
2025-09-14 23:54:45,630 - root - INFO - ClickHouse storage initialized
2025-09-14 23:54:45,630 - root - INFO - All components initialized successfully
2025-09-14 23:54:45,630 - root - INFO - Starting message monitoring...
2025-09-14 23:54:45,631 - telegram_client - INFO - Started monitoring channels for new messages
2025-09-14 23:55:00,645 - root - INFO - Parser statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-14 23:58:03,416 - root - INFO - Received signal 2, shutting down gracefully...
2025-09-14 23:58:03,828 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-14 23:58:03,829 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-14 23:58:03,833 - root - INFO - Cleaning up resources...
2025-09-14 23:58:03,833 - telethon.network.mtprotosender - INFO - Not disconnecting (already have no connection)
2025-09-14 23:58:03,835 - telegram_client - INFO - Disconnected from Telegram
2025-09-14 23:58:03,835 - storage - INFO - ClickHouse connection closed
2025-09-14 23:58:03,835 - root - INFO - Cleanup complete
2025-09-14 23:58:05,204 - root - INFO - Starting Telegram Tip Extractor
2025-09-14 23:58:05,204 - root - INFO - CSV logger initialized
2025-09-14 23:58:05,208 - utils - INFO - Loaded 2142 stocks from data/stock_list.csv
2025-09-14 23:58:05,208 - root - INFO - Message parser initialized
2025-09-14 23:58:05,209 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-14 23:58:05,209 - telethon.network.mtprotosender - DEBUG - Connection attempt 1...
2025-09-14 23:58:05,270 - telethon.network.mtprotosender - DEBUG - Connection success!
2025-09-14 23:58:05,270 - telethon.network.mtprotosender - DEBUG - Starting send loop
2025-09-14 23:58:05,270 - telethon.network.mtprotosender - DEBUG - Starting receive loop
2025-09-14 23:58:05,270 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-14 23:58:05,274 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-14 23:58:05,274 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013424644809300 to InvokeWithLayerRequest (740107e96360)
2025-09-14 23:58:05,274 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 80 bytes for sending
2025-09-14 23:58:05,274 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-14 23:58:05,274 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-14 23:58:05,274 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-14 23:58:05,336 - telethon.network.mtprotosender - DEBUG - Handling bad salt for message 7550013424644809300
2025-09-14 23:58:05,336 - telethon.network.mtprotosender - DEBUG - 1 message(s) will be resent
2025-09-14 23:58:05,336 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-14 23:58:05,336 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013424894998128 to InvokeWithLayerRequest (740107e96360)
2025-09-14 23:58:05,336 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 80 bytes for sending
2025-09-14 23:58:05,336 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-14 23:58:05,337 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-14 23:58:05,337 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013424896033820 to MsgsAck (740108377ec0)
2025-09-14 23:58:05,337 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-14 23:58:05,337 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-14 23:58:05,337 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-14 23:58:05,394 - telethon.network.mtprotosender - DEBUG - Handling container
2025-09-14 23:58:05,395 - telethon.network.mtprotosender - DEBUG - Handling new session created
2025-09-14 23:58:05,395 - telethon.network.mtprotosender - DEBUG - Handling acknowledge for [7550013424894998128]
2025-09-14 23:58:05,395 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-14 23:58:05,405 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550013424894998128
2025-09-14 23:58:05,405 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-14 23:58:05,405 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013425170962864 to GetUsersRequest (740109aff9e0)
2025-09-14 23:58:05,405 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-09-14 23:58:05,405 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-14 23:58:05,406 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-14 23:58:05,406 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013425172010000 to MsgsAck (7401099561e0)
2025-09-14 23:58:05,406 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 60 bytes for sending
2025-09-14 23:58:05,406 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-14 23:58:05,406 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-14 23:58:05,477 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550013425170962864
2025-09-14 23:58:05,478 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-14 23:58:05,478 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013425462461052 to GetStateRequest (740109aff9e0)
2025-09-14 23:58:05,478 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 20 bytes for sending
2025-09-14 23:58:05,479 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-14 23:58:05,479 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-14 23:58:05,479 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013425464584884 to MsgsAck (7401083741a0)
2025-09-14 23:58:05,479 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-14 23:58:05,479 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-14 23:58:05,479 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-14 23:58:05,545 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550013425462461052
2025-09-14 23:58:05,545 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-14 23:58:05,545 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013425731707152 to GetUsersRequest (740107e96960)
2025-09-14 23:58:05,546 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-09-14 23:58:05,546 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-14 23:58:05,546 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-14 23:58:05,546 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013425733636436 to MsgsAck (740107e96480)
2025-09-14 23:58:05,546 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-14 23:58:05,546 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-14 23:58:05,546 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-14 23:58:05,610 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550013425731707152
2025-09-14 23:58:05,611 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-14 23:58:05,611 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013425994559820 to GetUsersRequest (740107e96960)
2025-09-14 23:58:05,611 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-09-14 23:58:05,612 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-14 23:58:05,612 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-14 23:58:05,612 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013425997062260 to MsgsAck (74011c175fd0)
2025-09-14 23:58:05,612 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-14 23:58:05,612 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-14 23:58:05,612 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-14 23:58:05,673 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550013425994559820
2025-09-14 23:58:05,673 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-14 23:58:05,674 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-14 23:58:05,674 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013426245230252 to GetContactsRequest (740107e96c00)
2025-09-14 23:58:05,674 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 28 bytes for sending
2025-09-14 23:58:05,674 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-14 23:58:05,674 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-14 23:58:05,674 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013426246569212 to MsgsAck (740107e964e0)
2025-09-14 23:58:05,674 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-14 23:58:05,674 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-14 23:58:05,674 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-14 23:58:05,971 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550013426245230252
2025-09-14 23:58:05,996 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-14 23:58:06,006 - telegram_client - DEBUG - Direct resolution failed for 3065061158: Cannot find any entity corresponding to "3065061158"
2025-09-14 23:58:06,007 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013427872438608 to GetChannelsRequest (74010a5028a0)
2025-09-14 23:58:06,007 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 48 bytes for sending
2025-09-14 23:58:06,007 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-14 23:58:06,007 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-14 23:58:06,007 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013427874186692 to MsgsAck (74010a5c27b0)
2025-09-14 23:58:06,007 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-14 23:58:06,008 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-14 23:58:06,008 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-14 23:58:06,072 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550013427872438608
2025-09-14 23:58:06,072 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-14 23:58:06,072 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013428134054360 to GetChannelsRequest (740119f638f0)
2025-09-14 23:58:06,072 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 48 bytes for sending
2025-09-14 23:58:06,073 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-14 23:58:06,073 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-14 23:58:06,073 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013428136890588 to MsgsAck (7401097c7b60)
2025-09-14 23:58:06,073 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-14 23:58:06,073 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-14 23:58:06,073 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-14 23:58:06,136 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550013428134054360
2025-09-14 23:58:06,136 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-14 23:58:06,137 - telegram_client - INFO - Added channel: Channel TEST for ScorePandit (3065061158)
2025-09-14 23:58:06,137 - telegram_client - INFO - Successfully setup 1 channels
2025-09-14 23:58:06,176 - storage - INFO - Successfully connected to ClickHouse
2025-09-14 23:58:06,178 - storage - INFO - Table enriched_messages created/verified
2025-09-14 23:58:06,178 - root - INFO - ClickHouse storage initialized
2025-09-14 23:58:06,179 - root - INFO - All components initialized successfully
2025-09-14 23:58:06,179 - root - INFO - Starting message monitoring...
2025-09-14 23:58:06,179 - telegram_client - INFO - Started monitoring channels for new messages
2025-09-14 23:58:06,179 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013428560364900 to GetStateRequest (740108a2f110)
2025-09-14 23:58:06,179 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 20 bytes for sending
2025-09-14 23:58:06,179 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-14 23:58:06,179 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-14 23:58:06,179 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013428561989008 to MsgsAck (740107e97c80)
2025-09-14 23:58:06,179 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-14 23:58:06,180 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-14 23:58:06,180 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-14 23:58:06,243 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550013428560364900
2025-09-14 23:58:06,243 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-14 23:58:13,463 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-09-14 23:58:13,463 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-14 23:58:14,561 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-09-14 23:58:14,561 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-14 23:58:22,448 - telethon.network.mtprotosender - DEBUG - Handling gzipped data
2025-09-14 23:58:22,449 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-09-14 23:58:22,449 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-14 23:58:25,842 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-09-14 23:58:25,843 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-14 23:58:35,865 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-09-14 23:58:35,866 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-14 23:58:41,045 - telethon.network.mtprotosender - DEBUG - Handling gzipped data
2025-09-14 23:58:41,045 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-09-14 23:58:41,045 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-14 23:59:05,549 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013683445759964 to PingRequest (7401088fa360)
2025-09-14 23:59:05,550 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 28 bytes for sending
2025-09-14 23:59:05,550 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-14 23:59:05,550 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-14 23:59:05,550 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013683447276308 to MsgsAck (740108f000e0)
2025-09-14 23:59:05,550 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 84 bytes for sending
2025-09-14 23:59:05,550 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-14 23:59:05,550 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-14 23:59:05,606 - telethon.network.mtprotosender - DEBUG - Handling pong for message 7550013683445759964
2025-09-14 23:59:05,607 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-14 23:59:06,282 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-09-14 23:59:06,282 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-14 23:59:44,888 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-09-14 23:59:44,888 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:00:00,292 - root - INFO - Parser statistics: {'total_messages': 0, 'parsed_messages': 0, 'failed_messages': 0, 'success_rate': 0.0}
2025-09-15 00:00:01,446 - root - INFO - Received signal 2, shutting down gracefully...
2025-09-15 00:00:02,294 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-15 00:00:02,294 - telethon.network.mtprotosender - DEBUG - Closing current connection...
2025-09-15 00:00:02,295 - telethon.network.mtprotosender - DEBUG - Cancelling 0 pending message(s)...
2025-09-15 00:00:02,295 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-15 00:00:02,299 - root - INFO - Cleaning up resources...
2025-09-15 00:00:02,299 - telethon.network.mtprotosender - INFO - Not disconnecting (already have no connection)
2025-09-15 00:00:02,300 - telegram_client - INFO - Disconnected from Telegram
2025-09-15 00:00:02,300 - storage - INFO - ClickHouse connection closed
2025-09-15 00:00:02,300 - root - INFO - Cleanup complete
2025-09-15 00:00:03,842 - root - INFO - Starting Telegram Tip Extractor
2025-09-15 00:00:03,843 - root - INFO - CSV logger initialized
2025-09-15 00:00:03,847 - utils - INFO - Loaded 2142 stocks from data/stock_list.csv
2025-09-15 00:00:03,847 - root - INFO - Message parser initialized
2025-09-15 00:00:03,848 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-15 00:00:03,848 - telethon.network.mtprotosender - DEBUG - Connection attempt 1...
2025-09-15 00:00:03,904 - telethon.network.mtprotosender - DEBUG - Connection success!
2025-09-15 00:00:03,904 - telethon.network.mtprotosender - DEBUG - Starting send loop
2025-09-15 00:00:03,904 - telethon.network.mtprotosender - DEBUG - Starting receive loop
2025-09-15 00:00:03,905 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-15 00:00:03,907 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:00:03,907 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013933984447088 to InvokeWithLayerRequest (7fe11b533170)
2025-09-15 00:00:03,907 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 80 bytes for sending
2025-09-15 00:00:03,907 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:00:03,907 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:00:03,907 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:00:03,964 - telethon.network.mtprotosender - DEBUG - Handling bad salt for message 7550013933984447088
2025-09-15 00:00:03,964 - telethon.network.mtprotosender - DEBUG - 1 message(s) will be resent
2025-09-15 00:00:03,964 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:00:03,964 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013934212648000 to InvokeWithLayerRequest (7fe11b533170)
2025-09-15 00:00:03,964 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 80 bytes for sending
2025-09-15 00:00:03,964 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:00:03,964 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:00:03,964 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013934213639820 to MsgsAck (7fe11aee2060)
2025-09-15 00:00:03,964 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:00:03,965 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:00:03,965 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:00:04,021 - telethon.network.mtprotosender - DEBUG - Handling container
2025-09-15 00:00:04,021 - telethon.network.mtprotosender - DEBUG - Handling new session created
2025-09-15 00:00:04,021 - telethon.network.mtprotosender - DEBUG - Handling acknowledge for [7550013934212648000]
2025-09-15 00:00:04,021 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:00:04,066 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550013934212648000
2025-09-15 00:00:04,067 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:00:04,067 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013934919163908 to GetUsersRequest (7fe11c7d4230)
2025-09-15 00:00:04,067 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-09-15 00:00:04,067 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:00:04,068 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:00:04,068 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013934921327796 to MsgsAck (7fe11cdd2f60)
2025-09-15 00:00:04,068 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 60 bytes for sending
2025-09-15 00:00:04,068 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:00:04,068 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:00:04,143 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550013934919163908
2025-09-15 00:00:04,144 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:00:04,144 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013935227726188 to GetStateRequest (7fe11c7d4230)
2025-09-15 00:00:04,144 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 20 bytes for sending
2025-09-15 00:00:04,145 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:00:04,145 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:00:04,145 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013935229801384 to MsgsAck (7fe11cb2fb60)
2025-09-15 00:00:04,145 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:00:04,145 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:00:04,145 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:00:04,207 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550013935227726188
2025-09-15 00:00:04,207 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:00:04,207 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013935480469908 to GetUsersRequest (7fe11b115c10)
2025-09-15 00:00:04,208 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-09-15 00:00:04,208 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:00:04,208 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:00:04,208 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013935483136384 to MsgsAck (7fe11b533170)
2025-09-15 00:00:04,208 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:00:04,208 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:00:04,209 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:00:04,287 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550013935480469908
2025-09-15 00:00:04,287 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:00:04,288 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013935801450936 to GetUsersRequest (7fe1243d1b50)
2025-09-15 00:00:04,288 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-09-15 00:00:04,288 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:00:04,288 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:00:04,288 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013935803672996 to MsgsAck (7fe11b115c10)
2025-09-15 00:00:04,288 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:00:04,289 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:00:04,289 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:00:04,352 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550013935801450936
2025-09-15 00:00:04,352 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:00:04,352 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-15 00:00:04,352 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013936059716428 to GetContactsRequest (7fe11aee2330)
2025-09-15 00:00:04,352 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 28 bytes for sending
2025-09-15 00:00:04,353 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:00:04,353 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:00:04,353 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013936061643804 to MsgsAck (7fe11aee2960)
2025-09-15 00:00:04,353 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:00:04,353 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:00:04,353 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:00:04,643 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550013936059716428
2025-09-15 00:00:04,667 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:00:04,677 - telegram_client - DEBUG - Direct resolution failed for 3065061158: Cannot find any entity corresponding to "3065061158"
2025-09-15 00:00:04,678 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013937362653936 to GetChannelsRequest (7fe11aee2900)
2025-09-15 00:00:04,678 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 48 bytes for sending
2025-09-15 00:00:04,678 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:00:04,678 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:00:04,678 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013937364032952 to MsgsAck (7fe11cb079e0)
2025-09-15 00:00:04,678 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:00:04,678 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:00:04,679 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:00:04,746 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550013937362653936
2025-09-15 00:00:04,746 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:00:04,747 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013937638322080 to GetChannelsRequest (7fe11ba80980)
2025-09-15 00:00:04,747 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 48 bytes for sending
2025-09-15 00:00:04,747 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:00:04,747 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:00:04,747 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013937640165536 to MsgsAck (7fe11b1158e0)
2025-09-15 00:00:04,747 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:00:04,748 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:00:04,748 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:00:04,813 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550013937638322080
2025-09-15 00:00:04,813 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:00:04,813 - telegram_client - INFO - Added channel: Channel TEST for ScorePandit (3065061158)
2025-09-15 00:00:04,813 - telegram_client - INFO - Successfully setup 1 channels
2025-09-15 00:00:04,859 - storage - INFO - Successfully connected to ClickHouse
2025-09-15 00:00:04,861 - storage - INFO - Table enriched_messages created/verified
2025-09-15 00:00:04,861 - root - INFO - ClickHouse storage initialized
2025-09-15 00:00:04,861 - root - INFO - All components initialized successfully
2025-09-15 00:00:04,861 - root - INFO - Starting message monitoring...
2025-09-15 00:00:04,861 - telegram_client - INFO - Started monitoring channels for new messages
2025-09-15 00:00:04,861 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013938096607412 to GetStateRequest (7fe11b15c950)
2025-09-15 00:00:04,861 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 20 bytes for sending
2025-09-15 00:00:04,862 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:00:04,862 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:00:04,862 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550013938097936836 to MsgsAck (7fe11ce0fad0)
2025-09-15 00:00:04,862 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:00:04,862 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:00:04,862 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:00:04,927 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550013938096607412
2025-09-15 00:00:04,927 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:00:19,440 - telethon.network.mtprotosender - DEBUG - Handling gzipped data
2025-09-15 00:00:19,440 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-09-15 00:00:19,440 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:00:26,484 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-09-15 00:00:26,484 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:00:28,837 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-09-15 00:00:28,837 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:01:04,218 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014193219238144 to PingRequest (7fe11ba82a80)
2025-09-15 00:01:04,218 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 28 bytes for sending
2025-09-15 00:01:04,218 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:01:04,218 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:01:04,218 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014193222304208 to MsgsAck (7fe11d05b440)
2025-09-15 00:01:04,218 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 60 bytes for sending
2025-09-15 00:01:04,219 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:01:04,219 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:01:04,275 - telethon.network.mtprotosender - DEBUG - Handling pong for message 7550014193219238144
2025-09-15 00:01:04,276 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:01:27,205 - root - INFO - Received signal 2, shutting down gracefully...
2025-09-15 00:01:27,948 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-15 00:01:27,948 - telethon.network.mtprotosender - DEBUG - Closing current connection...
2025-09-15 00:01:27,949 - telethon.network.mtprotosender - DEBUG - Cancelling 0 pending message(s)...
2025-09-15 00:01:27,949 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-15 00:01:27,954 - root - INFO - Cleaning up resources...
2025-09-15 00:01:27,954 - telethon.network.mtprotosender - INFO - Not disconnecting (already have no connection)
2025-09-15 00:01:27,956 - telegram_client - INFO - Disconnected from Telegram
2025-09-15 00:01:27,956 - storage - INFO - ClickHouse connection closed
2025-09-15 00:01:27,956 - root - INFO - Cleanup complete
2025-09-15 00:01:31,013 - root - INFO - Starting Telegram Tip Extractor
2025-09-15 00:01:31,013 - root - INFO - CSV logger initialized
2025-09-15 00:01:31,018 - utils - INFO - Loaded 2142 stocks from data/stock_list.csv
2025-09-15 00:01:31,018 - root - INFO - Message parser initialized
2025-09-15 00:01:31,019 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-15 00:01:31,019 - telethon.network.mtprotosender - DEBUG - Connection attempt 1...
2025-09-15 00:01:31,073 - telethon.network.mtprotosender - DEBUG - Connection success!
2025-09-15 00:01:31,074 - telethon.network.mtprotosender - DEBUG - Starting send loop
2025-09-15 00:01:31,074 - telethon.network.mtprotosender - DEBUG - Starting receive loop
2025-09-15 00:01:31,074 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-15 00:01:31,077 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:01:31,077 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014308619548604 to InvokeWithLayerRequest (7df628f356a0)
2025-09-15 00:01:31,077 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 80 bytes for sending
2025-09-15 00:01:31,077 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:01:31,077 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:01:31,077 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:01:31,133 - telethon.network.mtprotosender - DEBUG - Handling bad salt for message 7550014308619548604
2025-09-15 00:01:31,133 - telethon.network.mtprotosender - DEBUG - 1 message(s) will be resent
2025-09-15 00:01:31,133 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:01:31,133 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014308845044896 to InvokeWithLayerRequest (7df628f356a0)
2025-09-15 00:01:31,133 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 80 bytes for sending
2025-09-15 00:01:31,133 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:01:31,133 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:01:31,133 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014308846040532 to MsgsAck (7df635a99d00)
2025-09-15 00:01:31,133 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:01:31,133 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:01:31,133 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:01:31,190 - telethon.network.mtprotosender - DEBUG - Handling container
2025-09-15 00:01:31,190 - telethon.network.mtprotosender - DEBUG - Handling new session created
2025-09-15 00:01:31,190 - telethon.network.mtprotosender - DEBUG - Handling acknowledge for [7550014308845044896]
2025-09-15 00:01:31,191 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:01:31,200 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550014308845044896
2025-09-15 00:01:31,201 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:01:31,201 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014309116536900 to GetUsersRequest (7df628f356a0)
2025-09-15 00:01:31,201 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-09-15 00:01:31,201 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:01:31,201 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:01:31,201 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014309117599292 to MsgsAck (7df628ee8e00)
2025-09-15 00:01:31,201 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 60 bytes for sending
2025-09-15 00:01:31,201 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:01:31,201 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:01:31,279 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550014309116536900
2025-09-15 00:01:31,279 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:01:31,279 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014309430771632 to GetStateRequest (7df62850f230)
2025-09-15 00:01:31,280 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 20 bytes for sending
2025-09-15 00:01:31,280 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:01:31,280 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:01:31,280 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014309431996152 to MsgsAck (7df62a4ac3e0)
2025-09-15 00:01:31,280 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:01:31,280 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:01:31,280 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:01:31,342 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550014309430771632
2025-09-15 00:01:31,342 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:01:31,342 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014309682585524 to GetUsersRequest (7df63c5b3fb0)
2025-09-15 00:01:31,342 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-09-15 00:01:31,343 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:01:31,343 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:01:31,343 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014309683874888 to MsgsAck (7df627e4a990)
2025-09-15 00:01:31,343 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:01:31,343 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:01:31,343 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:01:31,406 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550014309682585524
2025-09-15 00:01:31,406 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:01:31,406 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014309937322424 to GetUsersRequest (7df628a41940)
2025-09-15 00:01:31,406 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-09-15 00:01:31,406 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:01:31,406 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:01:31,407 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014309939354704 to MsgsAck (7df628ee8c20)
2025-09-15 00:01:31,407 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:01:31,407 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:01:31,407 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:01:31,470 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550014309937322424
2025-09-15 00:01:31,470 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:01:31,471 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-15 00:01:31,471 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014310196461484 to GetContactsRequest (7df627e4a780)
2025-09-15 00:01:31,471 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 28 bytes for sending
2025-09-15 00:01:31,471 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:01:31,471 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:01:31,471 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014310198469920 to MsgsAck (7df63c5b3fb0)
2025-09-15 00:01:31,471 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:01:31,472 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:01:31,472 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:01:31,798 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550014310196461484
2025-09-15 00:01:31,808 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:01:31,812 - telegram_client - DEBUG - Direct resolution failed for 3065061158: Cannot find any entity corresponding to "3065061158"
2025-09-15 00:01:31,813 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014311564277456 to GetChannelsRequest (7df637723e60)
2025-09-15 00:01:31,813 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 48 bytes for sending
2025-09-15 00:01:31,813 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:01:31,813 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:01:31,813 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014311565603064 to MsgsAck (7df62974e540)
2025-09-15 00:01:31,813 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:01:31,813 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:01:31,813 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:01:31,877 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550014311564277456
2025-09-15 00:01:31,877 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:01:31,877 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014311822979732 to GetChannelsRequest (7df629bef530)
2025-09-15 00:01:31,878 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 48 bytes for sending
2025-09-15 00:01:31,878 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:01:31,878 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:01:31,878 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014311825331492 to MsgsAck (7df6289c3320)
2025-09-15 00:01:31,878 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:01:31,878 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:01:31,878 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:01:31,940 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550014311822979732
2025-09-15 00:01:31,940 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:01:31,940 - telegram_client - INFO - Added channel: Channel TEST for ScorePandit (3065061158)
2025-09-15 00:01:31,940 - telegram_client - INFO - Successfully setup 1 channels
2025-09-15 00:01:31,983 - storage - INFO - Successfully connected to ClickHouse
2025-09-15 00:01:31,986 - storage - INFO - Table enriched_messages created/verified
2025-09-15 00:01:31,986 - root - INFO - ClickHouse storage initialized
2025-09-15 00:01:31,986 - root - INFO - All components initialized successfully
2025-09-15 00:01:31,986 - root - INFO - Fetching historical messages from last 5 hours...
2025-09-15 00:01:31,986 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014312256960676 to GetHistoryRequest (7df6280167e0)
2025-09-15 00:01:31,986 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 72 bytes for sending
2025-09-15 00:01:31,986 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:01:31,986 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:01:31,986 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014312258387372 to MsgsAck (7df627e4a630)
2025-09-15 00:01:31,986 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:01:31,987 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:01:31,987 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:01:32,111 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550014312256960676
2025-09-15 00:01:32,115 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:01:32,117 - telegram_client - INFO - Fetched 14 recent messages from Channel TEST for ScorePandit
2025-09-15 00:01:33,119 - telegram_client - INFO - Fetched total 14 recent messages
2025-09-15 00:01:33,119 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 140
2025-09-15 00:01:33,119 - parser - DEBUG - Skipping empty or too short message
2025-09-15 00:01:33,120 - root - DEBUG - Message from Channel TEST for ScorePandit was not a trading tip
2025-09-15 00:01:33,120 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 139
2025-09-15 00:01:33,120 - parser - DEBUG - Skipping empty or too short message
2025-09-15 00:01:33,120 - root - DEBUG - Message from Channel TEST for ScorePandit was not a trading tip
2025-09-15 00:01:33,120 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 138
2025-09-15 00:01:33,120 - parser - DEBUG - Skipping empty or too short message
2025-09-15 00:01:33,120 - root - DEBUG - Message from Channel TEST for ScorePandit was not a trading tip
2025-09-15 00:01:33,121 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 137
2025-09-15 00:01:33,121 - parser - DEBUG - Message doesn't appear to be a trading tip
2025-09-15 00:01:33,121 - root - DEBUG - Message from Channel TEST for ScorePandit was not a trading tip
2025-09-15 00:01:33,121 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 136
2025-09-15 00:01:33,121 - parser - DEBUG - Skipping empty or too short message
2025-09-15 00:01:33,121 - root - DEBUG - Message from Channel TEST for ScorePandit was not a trading tip
2025-09-15 00:01:33,122 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 135
2025-09-15 00:01:33,122 - parser - DEBUG - Skipping empty or too short message
2025-09-15 00:01:33,122 - root - DEBUG - Message from Channel TEST for ScorePandit was not a trading tip
2025-09-15 00:01:33,122 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 134
2025-09-15 00:01:33,122 - parser - DEBUG - Message doesn't appear to be a trading tip
2025-09-15 00:01:33,122 - root - DEBUG - Message from Channel TEST for ScorePandit was not a trading tip
2025-09-15 00:01:33,122 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 133
2025-09-15 00:01:33,129 - parser - DEBUG - Successfully parsed message: PEL
2025-09-15 00:01:33,129 - storage - DEBUG - Logged enriched message: PEL
2025-09-15 00:01:33,133 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-15 00:01:33,133 - root - INFO - Processed trading tip: PEL from Channel TEST for ScorePandit
2025-09-15 00:01:33,133 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 132
2025-09-15 00:01:33,133 - parser - DEBUG - Successfully parsed message: IGL
2025-09-15 00:01:33,134 - storage - DEBUG - Logged enriched message: IGL
2025-09-15 00:01:33,137 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-15 00:01:33,138 - root - INFO - Processed trading tip: IGL from Channel TEST for ScorePandit
2025-09-15 00:01:33,138 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 131
2025-09-15 00:01:33,140 - parser - DEBUG - Successfully parsed message: RELIANCE
2025-09-15 00:01:33,141 - storage - DEBUG - Logged enriched message: RELIANCE
2025-09-15 00:01:33,144 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-15 00:01:33,144 - root - INFO - Processed trading tip: RELIANCE from Channel TEST for ScorePandit
2025-09-15 00:01:33,144 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 130
2025-09-15 00:01:33,144 - parser - DEBUG - Successfully parsed message: ABCAPITAL
2025-09-15 00:01:33,145 - storage - DEBUG - Logged enriched message: ABCAPITAL
2025-09-15 00:01:33,147 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-15 00:01:33,147 - root - INFO - Processed trading tip: ABCAPITAL from Channel TEST for ScorePandit
2025-09-15 00:01:33,148 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 129
2025-09-15 00:01:33,148 - parser - DEBUG - Successfully parsed message: HAL
2025-09-15 00:01:33,148 - storage - DEBUG - Logged enriched message: HAL
2025-09-15 00:01:33,151 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-15 00:01:33,151 - root - INFO - Processed trading tip: HAL from Channel TEST for ScorePandit
2025-09-15 00:01:33,151 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 128
2025-09-15 00:01:33,151 - parser - DEBUG - Message doesn't appear to be a trading tip
2025-09-15 00:01:33,151 - root - DEBUG - Message from Channel TEST for ScorePandit was not a trading tip
2025-09-15 00:01:33,151 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 127
2025-09-15 00:01:33,151 - parser - DEBUG - Skipping empty or too short message
2025-09-15 00:01:33,151 - root - DEBUG - Message from Channel TEST for ScorePandit was not a trading tip
2025-09-15 00:01:33,152 - root - INFO - Historical processing complete. Statistics: {'total_messages': 14, 'parsed_messages': 5, 'failed_messages': 0, 'success_rate': 35.71}
2025-09-15 00:01:33,152 - root - INFO - Cleaning up resources...
2025-09-15 00:01:33,152 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-15 00:01:33,152 - telethon.network.mtprotosender - DEBUG - Closing current connection...
2025-09-15 00:01:33,152 - telethon.network.mtprotosender - DEBUG - Cancelling 0 pending message(s)...
2025-09-15 00:01:33,152 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-15 00:01:33,156 - telegram_client - INFO - Disconnected from Telegram
2025-09-15 00:01:33,156 - storage - INFO - ClickHouse connection closed
2025-09-15 00:01:33,156 - root - INFO - Cleanup complete
2025-09-15 00:03:17,392 - root - INFO - Starting Telegram Tip Extractor
2025-09-15 00:03:17,392 - root - INFO - CSV logger initialized
2025-09-15 00:03:17,396 - utils - INFO - Loaded 2142 stocks from data/stock_list.csv
2025-09-15 00:03:17,396 - root - INFO - Message parser initialized
2025-09-15 00:03:17,397 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-15 00:03:17,397 - telethon.network.mtprotosender - DEBUG - Connection attempt 1...
2025-09-15 00:03:17,467 - telethon.network.mtprotosender - DEBUG - Connection success!
2025-09-15 00:03:17,468 - telethon.network.mtprotosender - DEBUG - Starting send loop
2025-09-15 00:03:17,468 - telethon.network.mtprotosender - DEBUG - Starting receive loop
2025-09-15 00:03:17,468 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-15 00:03:17,472 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:03:17,472 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014765467427536 to InvokeWithLayerRequest (79d66953e8a0)
2025-09-15 00:03:17,472 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 80 bytes for sending
2025-09-15 00:03:17,472 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:03:17,472 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:03:17,472 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:03:17,542 - telethon.network.mtprotosender - DEBUG - Handling bad salt for message 7550014765467427536
2025-09-15 00:03:17,542 - telethon.network.mtprotosender - DEBUG - 1 message(s) will be resent
2025-09-15 00:03:17,542 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:03:17,542 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014765749465272 to InvokeWithLayerRequest (79d66953e8a0)
2025-09-15 00:03:17,543 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 80 bytes for sending
2025-09-15 00:03:17,543 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:03:17,543 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:03:17,543 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014765750592516 to MsgsAck (79d66abdc620)
2025-09-15 00:03:17,543 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:03:17,543 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:03:17,543 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:03:17,616 - telethon.network.mtprotosender - DEBUG - Handling container
2025-09-15 00:03:17,616 - telethon.network.mtprotosender - DEBUG - Handling new session created
2025-09-15 00:03:17,616 - telethon.network.mtprotosender - DEBUG - Handling acknowledge for [7550014765749465272]
2025-09-15 00:03:17,616 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:03:17,624 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550014765749465272
2025-09-15 00:03:17,624 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:03:17,625 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014766078115748 to GetUsersRequest (79d66953e8a0)
2025-09-15 00:03:17,625 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-09-15 00:03:17,625 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:03:17,625 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:03:17,625 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014766079279228 to MsgsAck (79d66ada7470)
2025-09-15 00:03:17,625 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 60 bytes for sending
2025-09-15 00:03:17,625 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:03:17,625 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:03:17,718 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550014766078115748
2025-09-15 00:03:17,719 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:03:17,719 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014766456454560 to GetStateRequest (79d66953e8a0)
2025-09-15 00:03:17,720 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 20 bytes for sending
2025-09-15 00:03:17,720 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:03:17,720 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:03:17,720 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014766460109996 to MsgsAck (79d6789561e0)
2025-09-15 00:03:17,720 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:03:17,720 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:03:17,720 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:03:17,801 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550014766456454560
2025-09-15 00:03:17,801 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:03:17,801 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014766784576700 to GetUsersRequest (79d6695c9c70)
2025-09-15 00:03:17,801 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-09-15 00:03:17,802 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:03:17,802 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:03:17,802 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014766787566468 to MsgsAck (79d668f6e8d0)
2025-09-15 00:03:17,802 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:03:17,802 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:03:17,803 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:03:17,879 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550014766784576700
2025-09-15 00:03:17,879 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:03:17,880 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014767098187732 to GetUsersRequest (79d66a442a80)
2025-09-15 00:03:17,880 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-09-15 00:03:17,880 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:03:17,880 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:03:17,880 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014767099309252 to MsgsAck (79d6695c9c70)
2025-09-15 00:03:17,880 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:03:17,880 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:03:17,880 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:03:17,957 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550014767098187732
2025-09-15 00:03:17,957 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:03:17,957 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-15 00:03:17,957 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014767408747956 to GetContactsRequest (79d669c4db50)
2025-09-15 00:03:17,957 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 28 bytes for sending
2025-09-15 00:03:17,957 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:03:17,957 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:03:17,958 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014767409721660 to MsgsAck (79d66ae85e80)
2025-09-15 00:03:17,958 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:03:17,958 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:03:17,958 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:03:18,277 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550014767408747956
2025-09-15 00:03:18,301 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:03:18,310 - telegram_client - DEBUG - Direct resolution failed for 3065061158: Cannot find any entity corresponding to "3065061158"
2025-09-15 00:03:18,311 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014769117389608 to GetChannelsRequest (79d669c4db50)
2025-09-15 00:03:18,311 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 48 bytes for sending
2025-09-15 00:03:18,311 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:03:18,311 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:03:18,311 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014769119132924 to MsgsAck (79d669cb0620)
2025-09-15 00:03:18,311 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:03:18,311 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:03:18,312 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:03:18,395 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550014769117389608
2025-09-15 00:03:18,395 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:03:18,395 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014769455051352 to GetChannelsRequest (79d67d642330)
2025-09-15 00:03:18,395 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 48 bytes for sending
2025-09-15 00:03:18,395 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:03:18,395 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:03:18,395 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014769456220556 to MsgsAck (79d66b661820)
2025-09-15 00:03:18,395 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:03:18,396 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:03:18,396 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:03:18,471 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550014769455051352
2025-09-15 00:03:18,471 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:03:18,471 - telegram_client - INFO - Added channel: Channel TEST for ScorePandit (3065061158)
2025-09-15 00:03:18,471 - telegram_client - INFO - Successfully setup 1 channels
2025-09-15 00:03:18,507 - storage - INFO - Successfully connected to ClickHouse
2025-09-15 00:03:18,510 - storage - INFO - Table enriched_messages created/verified
2025-09-15 00:03:18,510 - root - INFO - ClickHouse storage initialized
2025-09-15 00:03:18,510 - root - INFO - All components initialized successfully
2025-09-15 00:03:18,510 - root - INFO - Fetching historical messages from last 5 hours...
2025-09-15 00:03:18,510 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014769914860652 to GetHistoryRequest (79d66953d9d0)
2025-09-15 00:03:18,510 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 72 bytes for sending
2025-09-15 00:03:18,510 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:03:18,510 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:03:18,510 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550014769916099476 to MsgsAck (79d66916ac60)
2025-09-15 00:03:18,510 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:03:18,511 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:03:18,511 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:03:18,623 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550014769914860652
2025-09-15 00:03:18,625 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:03:18,626 - telegram_client - INFO - Fetched 14 recent messages from Channel TEST for ScorePandit
2025-09-15 00:03:19,628 - telegram_client - INFO - Fetched total 14 recent messages
2025-09-15 00:03:19,628 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 140
2025-09-15 00:03:19,629 - parser - DEBUG - Received message: Kiyu
2025-09-15 00:03:19,629 - parser - DEBUG - Message doesn't appear to be a trading tip
2025-09-15 00:03:19,630 - root - DEBUG - Message from Channel TEST for ScorePandit was not a trading tip
2025-09-15 00:03:19,630 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 139
2025-09-15 00:03:19,630 - parser - DEBUG - Received message: Samar
2025-09-15 00:03:19,630 - parser - DEBUG - Message doesn't appear to be a trading tip
2025-09-15 00:03:19,630 - root - DEBUG - Message from Channel TEST for ScorePandit was not a trading tip
2025-09-15 00:03:19,630 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 138
2025-09-15 00:03:19,630 - parser - DEBUG - Received message: Hello 123
2025-09-15 00:03:19,630 - parser - DEBUG - Message doesn't appear to be a trading tip
2025-09-15 00:03:19,630 - root - DEBUG - Message from Channel TEST for ScorePandit was not a trading tip
2025-09-15 00:03:19,630 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 137
2025-09-15 00:03:19,630 - parser - DEBUG - Received message: Message at 11.58
2025-09-15 00:03:19,630 - parser - DEBUG - Message doesn't appear to be a trading tip
2025-09-15 00:03:19,630 - root - DEBUG - Message from Channel TEST for ScorePandit was not a trading tip
2025-09-15 00:03:19,631 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 136
2025-09-15 00:03:19,631 - parser - DEBUG - Received message: Buy BEL
2025-09-15 00:03:19,631 - parser - DEBUG - Message doesn't appear to be a trading tip
2025-09-15 00:03:19,631 - root - DEBUG - Message from Channel TEST for ScorePandit was not a trading tip
2025-09-15 00:03:19,631 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 135
2025-09-15 00:03:19,631 - parser - DEBUG - Received message: Abs
2025-09-15 00:03:19,631 - parser - DEBUG - Message doesn't appear to be a trading tip
2025-09-15 00:03:19,631 - root - DEBUG - Message from Channel TEST for ScorePandit was not a trading tip
2025-09-15 00:03:19,631 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 134
2025-09-15 00:03:19,631 - parser - DEBUG - Received message: Buy poonwala
2025-09-15 00:03:19,631 - parser - DEBUG - Message doesn't appear to be a trading tip
2025-09-15 00:03:19,631 - root - DEBUG - Message from Channel TEST for ScorePandit was not a trading tip
2025-09-15 00:03:19,631 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 133
2025-09-15 00:03:19,631 - parser - DEBUG - Received message: Buy bata PE
2025-09-15 00:03:19,634 - parser - DEBUG - Successfully parsed message: PEL
2025-09-15 00:03:19,634 - storage - DEBUG - Logged enriched message: PEL
2025-09-15 00:03:19,637 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-15 00:03:19,637 - root - INFO - Processed trading tip: PEL from Channel TEST for ScorePandit
2025-09-15 00:03:19,638 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 132
2025-09-15 00:03:19,638 - parser - DEBUG - Received message: Buy IGL future 200
2025-09-15 00:03:19,638 - parser - DEBUG - Successfully parsed message: IGL
2025-09-15 00:03:19,638 - storage - DEBUG - Logged enriched message: IGL
2025-09-15 00:03:19,642 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-15 00:03:19,642 - root - INFO - Processed trading tip: IGL from Channel TEST for ScorePandit
2025-09-15 00:03:19,642 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 131
2025-09-15 00:03:19,642 - parser - DEBUG - Received message: Buy relance 1500
2025-09-15 00:03:19,645 - parser - DEBUG - Successfully parsed message: RELIANCE
2025-09-15 00:03:19,645 - storage - DEBUG - Logged enriched message: RELIANCE
2025-09-15 00:03:19,648 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-15 00:03:19,648 - root - INFO - Processed trading tip: RELIANCE from Channel TEST for ScorePandit
2025-09-15 00:03:19,648 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 130
2025-09-15 00:03:19,649 - parser - DEBUG - Received message: Buy abcapital 200 ce at 50
2025-09-15 00:03:19,649 - parser - DEBUG - Successfully parsed message: ABCAPITAL
2025-09-15 00:03:19,649 - storage - DEBUG - Logged enriched message: ABCAPITAL
2025-09-15 00:03:19,651 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-15 00:03:19,651 - root - INFO - Processed trading tip: ABCAPITAL from Channel TEST for ScorePandit
2025-09-15 00:03:19,652 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 129
2025-09-15 00:03:19,652 - parser - DEBUG - Received message: Buy hal 5000 ce at 31
2025-09-15 00:03:19,652 - parser - DEBUG - Successfully parsed message: HAL
2025-09-15 00:03:19,652 - storage - DEBUG - Logged enriched message: HAL
2025-09-15 00:03:19,654 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-15 00:03:19,654 - root - INFO - Processed trading tip: HAL from Channel TEST for ScorePandit
2025-09-15 00:03:19,654 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 128
2025-09-15 00:03:19,654 - parser - DEBUG - Received message: This is test msg
2025-09-15 00:03:19,655 - parser - DEBUG - Message doesn't appear to be a trading tip
2025-09-15 00:03:19,655 - root - DEBUG - Message from Channel TEST for ScorePandit was not a trading tip
2025-09-15 00:03:19,655 - storage - DEBUG - Logged raw message from Channel TEST for ScorePandit: 127
2025-09-15 00:03:19,655 - parser - DEBUG - Received message: Hello
2025-09-15 00:03:19,655 - parser - DEBUG - Message doesn't appear to be a trading tip
2025-09-15 00:03:19,655 - root - DEBUG - Message from Channel TEST for ScorePandit was not a trading tip
2025-09-15 00:03:19,655 - root - INFO - Historical processing complete. Statistics: {'total_messages': 14, 'parsed_messages': 5, 'failed_messages': 0, 'success_rate': 35.71}
2025-09-15 00:03:19,655 - root - INFO - Cleaning up resources...
2025-09-15 00:03:19,655 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-15 00:03:19,655 - telethon.network.mtprotosender - DEBUG - Closing current connection...
2025-09-15 00:03:19,655 - telethon.network.mtprotosender - DEBUG - Cancelling 0 pending message(s)...
2025-09-15 00:03:19,655 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-15 00:03:19,659 - telegram_client - INFO - Disconnected from Telegram
2025-09-15 00:03:19,659 - storage - INFO - ClickHouse connection closed
2025-09-15 00:03:19,659 - root - INFO - Cleanup complete
2025-09-15 00:04:44,104 - root - INFO - Starting Telegram Tip Extractor
2025-09-15 00:04:44,104 - root - INFO - CSV logger initialized
2025-09-15 00:04:44,108 - utils - INFO - Loaded 2142 stocks from data/stock_list.csv
2025-09-15 00:04:44,108 - root - INFO - Message parser initialized
2025-09-15 00:04:44,109 - telethon.network.mtprotosender - INFO - Connecting to *************:443/TcpFull...
2025-09-15 00:04:44,109 - telethon.network.mtprotosender - DEBUG - Connection attempt 1...
2025-09-15 00:04:44,179 - telethon.network.mtprotosender - DEBUG - Connection success!
2025-09-15 00:04:44,179 - telethon.network.mtprotosender - DEBUG - Starting send loop
2025-09-15 00:04:44,179 - telethon.network.mtprotosender - DEBUG - Starting receive loop
2025-09-15 00:04:44,179 - telethon.network.mtprotosender - INFO - Connection to *************:443/TcpFull complete!
2025-09-15 00:04:44,181 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:04:44,181 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550015137966582184 to InvokeWithLayerRequest (706a0f797470)
2025-09-15 00:04:44,181 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 80 bytes for sending
2025-09-15 00:04:44,181 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:04:44,182 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:04:44,182 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:04:44,252 - telethon.network.mtprotosender - DEBUG - Handling bad salt for message 7550015137966582184
2025-09-15 00:04:44,252 - telethon.network.mtprotosender - DEBUG - 1 message(s) will be resent
2025-09-15 00:04:44,252 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:04:44,252 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550015138250473860 to InvokeWithLayerRequest (706a0f797470)
2025-09-15 00:04:44,252 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 80 bytes for sending
2025-09-15 00:04:44,253 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:04:44,253 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:04:44,253 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550015138253042104 to MsgsAck (706a1039bc20)
2025-09-15 00:04:44,253 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:04:44,253 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:04:44,253 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:04:44,322 - telethon.network.mtprotosender - DEBUG - Handling container
2025-09-15 00:04:44,322 - telethon.network.mtprotosender - DEBUG - Handling new session created
2025-09-15 00:04:44,322 - telethon.network.mtprotosender - DEBUG - Handling acknowledge for [7550015138250473860]
2025-09-15 00:04:44,322 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:04:44,331 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550015138250473860
2025-09-15 00:04:44,331 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:04:44,332 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550015138568150404 to GetUsersRequest (706a1e143b60)
2025-09-15 00:04:44,332 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-09-15 00:04:44,332 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:04:44,332 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:04:44,332 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550015138569354896 to MsgsAck (706a0ed78e00)
2025-09-15 00:04:44,332 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 60 bytes for sending
2025-09-15 00:04:44,332 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:04:44,332 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:04:44,413 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550015138568150404
2025-09-15 00:04:44,413 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:04:44,414 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550015138896354560 to GetStateRequest (706a1e143b60)
2025-09-15 00:04:44,414 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 20 bytes for sending
2025-09-15 00:04:44,414 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:04:44,414 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:04:44,414 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550015138899236564 to MsgsAck (706a10923d40)
2025-09-15 00:04:44,414 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:04:44,415 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:04:44,415 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:04:44,488 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550015138896354560
2025-09-15 00:04:44,488 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:04:44,488 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550015139195410612 to GetUsersRequest (706a0f75d970)
2025-09-15 00:04:44,488 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-09-15 00:04:44,489 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:04:44,489 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:04:44,489 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550015139196463468 to MsgsAck (706a0e2965a0)
2025-09-15 00:04:44,489 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:04:44,489 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:04:44,489 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:04:44,563 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550015139195410612
2025-09-15 00:04:44,564 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:04:44,564 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550015139497975232 to GetUsersRequest (706a0e79d190)
2025-09-15 00:04:44,564 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-09-15 00:04:44,564 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:04:44,564 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:04:44,565 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550015139500033264 to MsgsAck (706a0f75d970)
2025-09-15 00:04:44,565 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:04:44,565 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:04:44,565 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:04:44,639 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550015139497975232
2025-09-15 00:04:44,639 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:04:44,639 - telegram_client - INFO - Connected to Telegram as KafkaGeek (@Kafkaexpert)
2025-09-15 00:04:44,639 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550015139798480872 to GetContactsRequest (706a0e296510)
2025-09-15 00:04:44,639 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 28 bytes for sending
2025-09-15 00:04:44,639 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:04:44,639 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:04:44,639 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550015139799451712 to MsgsAck (706a0e296300)
2025-09-15 00:04:44,639 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:04:44,640 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:04:44,640 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:04:44,943 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550015139798480872
2025-09-15 00:04:44,953 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:04:44,957 - telegram_client - DEBUG - Direct resolution failed for 1126813440: Cannot find any entity corresponding to "1126813440"
2025-09-15 00:04:44,958 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550015141072620276 to GetChannelsRequest (706a0eefab10)
2025-09-15 00:04:44,958 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 48 bytes for sending
2025-09-15 00:04:44,958 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:04:44,958 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:04:44,958 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550015141074715500 to MsgsAck (706a0ed29d00)
2025-09-15 00:04:44,958 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:04:44,958 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:04:44,959 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:04:45,033 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550015141072620276
2025-09-15 00:04:45,033 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:04:45,034 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550015141671063904 to GetChannelsRequest (706a1bfea810)
2025-09-15 00:04:45,034 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 48 bytes for sending
2025-09-15 00:04:45,034 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:04:45,034 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:04:45,034 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550015141672132020 to MsgsAck (706a109233e0)
2025-09-15 00:04:45,034 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:04:45,034 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:04:45,034 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:04:45,109 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550015141671063904
2025-09-15 00:04:45,109 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:04:45,110 - telegram_client - INFO - Added channel: 🚏iT'S Capita ® (1126813440)
2025-09-15 00:04:45,110 - telegram_client - INFO - Successfully setup 1 channels
2025-09-15 00:04:45,133 - storage - INFO - Successfully connected to ClickHouse
2025-09-15 00:04:45,135 - storage - INFO - Table enriched_messages created/verified
2025-09-15 00:04:45,136 - root - INFO - ClickHouse storage initialized
2025-09-15 00:04:45,136 - root - INFO - All components initialized successfully
2025-09-15 00:04:45,136 - root - INFO - Fetching historical messages from last 48 hours...
2025-09-15 00:04:45,136 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550015142080216892 to GetHistoryRequest (706a0e6edb20)
2025-09-15 00:04:45,136 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 72 bytes for sending
2025-09-15 00:04:45,136 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:04:45,136 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:04:45,136 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7550015142081689364 to MsgsAck (706a0e6eec00)
2025-09-15 00:04:45,136 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-09-15 00:04:45,136 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-09-15 00:04:45,137 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-09-15 00:04:45,343 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7550015142080216892
2025-09-15 00:04:45,350 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-09-15 00:04:45,354 - telegram_client - INFO - Fetched 5 recent messages from 🚏iT'S Capita ®
2025-09-15 00:04:46,355 - telegram_client - INFO - Fetched total 5 recent messages
2025-09-15 00:04:46,355 - storage - DEBUG - Logged raw message from 🚏iT'S Capita ®: 93766
2025-09-15 00:04:46,356 - parser - DEBUG - Received message: **JOIN OUR CHANNEL  FOR DAILY JACKPOT  BANKNIFTY CALLS AND START BUILTING UP UR CAPITAL ...

Daily  Our Banknifty Calls gained 200..300+++ points 😍😍😍

OUR ONE CALL IS ENOUGH TO EARN AWESOME QUALITY PROFIT

****https://t.me/+yg1wqy5sLSoxMzE1**
2025-09-15 00:04:46,361 - parser - DEBUG - Successfully parsed message: NIFTY
2025-09-15 00:04:46,362 - storage - DEBUG - Logged enriched message: NIFTY
2025-09-15 00:04:46,366 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-15 00:04:46,366 - root - INFO - Processed trading tip: NIFTY from 🚏iT'S Capita ®
2025-09-15 00:04:46,366 - storage - DEBUG - Logged raw message from 🚏iT'S Capita ®: 93763
2025-09-15 00:04:46,367 - parser - DEBUG - Received message: 
2025-09-15 00:04:46,367 - parser - DEBUG - Skipping empty or too short message
2025-09-15 00:04:46,367 - root - DEBUG - Message from 🚏iT'S Capita ® was not a trading tip
2025-09-15 00:04:46,367 - storage - DEBUG - Logged raw message from 🚏iT'S Capita ®: 93762
2025-09-15 00:04:46,367 - parser - DEBUG - Received message: **Hello all,

On heavy public demand finally We have created  Group for free trial for 15 days only 1st 100 members can join...


✅DAILY ONE CALL BUT BEST RESEARCHED TRADE FROM 



✅SEBI REGISTERED RA**

**https://t.me/+AKUUnY8qJ6c4NjNl**
2025-09-15 00:04:46,368 - parser - DEBUG - Successfully parsed message: UBL
2025-09-15 00:04:46,368 - storage - DEBUG - Logged enriched message: UBL
2025-09-15 00:04:46,371 - storage - ERROR - Error inserting into ClickHouse: Insert data column count does not match column names
2025-09-15 00:04:46,372 - root - INFO - Processed trading tip: UBL from 🚏iT'S Capita ®
2025-09-15 00:04:46,372 - storage - DEBUG - Logged raw message from 🚏iT'S Capita ®: 93761
2025-09-15 00:04:46,372 - parser - DEBUG - Received message: 
2025-09-15 00:04:46,372 - parser - DEBUG - Skipping empty or too short message
2025-09-15 00:04:46,372 - root - DEBUG - Message from 🚏iT'S Capita ® was not a trading tip
2025-09-15 00:04:46,372 - storage - DEBUG - Logged raw message from 🚏iT'S Capita ®: 93755
2025-09-15 00:04:46,372 - parser - DEBUG - Received message: Good Morning
2025-09-15 00:04:46,372 - parser - DEBUG - Message doesn't appear to be a trading tip
2025-09-15 00:04:46,372 - root - DEBUG - Message from 🚏iT'S Capita ® was not a trading tip
2025-09-15 00:04:46,372 - root - INFO - Historical processing complete. Statistics: {'total_messages': 5, 'parsed_messages': 2, 'failed_messages': 0, 'success_rate': 40.0}
2025-09-15 00:04:46,372 - root - INFO - Cleaning up resources...
2025-09-15 00:04:46,373 - telethon.network.mtprotosender - INFO - Disconnecting from *************:443/TcpFull...
2025-09-15 00:04:46,373 - telethon.network.mtprotosender - DEBUG - Closing current connection...
2025-09-15 00:04:46,373 - telethon.network.mtprotosender - DEBUG - Cancelling 0 pending message(s)...
2025-09-15 00:04:46,373 - telethon.network.mtprotosender - INFO - Disconnection from *************:443/TcpFull complete!
2025-09-15 00:04:46,376 - telegram_client - INFO - Disconnected from Telegram
2025-09-15 00:04:46,377 - storage - INFO - ClickHouse connection closed
2025-09-15 00:04:46,377 - root - INFO - Cleanup complete
