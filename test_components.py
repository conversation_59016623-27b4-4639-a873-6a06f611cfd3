#!/usr/bin/env python3
"""
Test script for Telegram Tip Extractor components.
Tests individual modules without requiring Telegram API access.
"""

import os
import sys
import tempfile
import csv
from datetime import datetime
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config_loader import ConfigLoader
from utils import StockMatcher, TextProcessor, clean_message_text, parse_timeframe
from parser import MessageParser
from storage import CSVLogger


def test_config_loader():
    """Test configuration loading."""
    print("Testing ConfigLoader...")
    
    try:
        loader = ConfigLoader("config.yaml")
        config = loader.load_config()
        
        print("✓ Configuration loaded successfully")
        print(f"  - Channels: {len(loader.get_channels())}")
        print(f"  - Stock list: {loader.get_file_paths()['stock_list']}")
        print(f"  - Fuzzy threshold: {loader.get_parsing_config()['fuzzy_match_threshold']}")
        
        return True
    except Exception as e:
        print(f"✗ Configuration loading failed: {e}")
        return False


def test_stock_matcher():
    """Test stock symbol matching."""
    print("\nTesting StockMatcher...")
    
    try:
        matcher = StockMatcher("data/stock_list.csv", threshold=80)
        
        # Test exact matches
        test_cases = [
            ("RELIANCE", "RELIANCE"),
            ("reliance industries", "RELIANCE"),
            ("TCS buy at 3500", "TCS"),
            ("HDFC Bank target 1600", "HDFCBANK"),
            ("Infosys long", "INFY"),
            ("random text", None),
        ]
        
        success_count = 0
        for test_text, expected in test_cases:
            result = matcher.find_stock_symbol(test_text)
            if result == expected:
                print(f"✓ '{test_text}' -> {result}")
                success_count += 1
            else:
                print(f"✗ '{test_text}' -> {result} (expected {expected})")
        
        print(f"Stock matching: {success_count}/{len(test_cases)} tests passed")
        return success_count == len(test_cases)
        
    except Exception as e:
        print(f"✗ StockMatcher test failed: {e}")
        return False


def test_text_processor():
    """Test text processing functions."""
    print("\nTesting TextProcessor...")
    
    try:
        processor = TextProcessor()
        
        # Test price extraction
        price_tests = [
            ("Buy at 2500", 2500.0),
            ("Entry price: 1450.50", 1450.50),
            ("@ 3200", 3200.0),
        ]
        
        for text, expected in price_tests:
            result = processor.extract_price(text)
            if result == expected:
                print(f"✓ Price extraction: '{text}' -> {result}")
            else:
                print(f"✗ Price extraction: '{text}' -> {result} (expected {expected})")
        
        # Test direction extraction
        direction_tests = [
            ("BUY RELIANCE", "Buy"),
            ("SELL TCS", "Sell"),
            ("Long position", "Buy"),
            ("Short HDFC", "Sell"),
        ]
        
        for text, expected in direction_tests:
            result = processor.extract_direction(text)
            if result == expected:
                print(f"✓ Direction extraction: '{text}' -> {result}")
            else:
                print(f"✗ Direction extraction: '{text}' -> {result} (expected {expected})")
        
        # Test target extraction
        targets_text = "Target: 2600, 2700, 2800"
        targets = processor.extract_targets(targets_text)
        expected_targets = [2600.0, 2700.0, 2800.0]
        if targets == expected_targets:
            print(f"✓ Target extraction: {targets}")
        else:
            print(f"✗ Target extraction: {targets} (expected {expected_targets})")
        
        # Test stop loss extraction
        sl_text = "Stop loss: 2400"
        sl = processor.extract_stop_loss(sl_text)
        if sl == 2400.0:
            print(f"✓ Stop loss extraction: {sl}")
        else:
            print(f"✗ Stop loss extraction: {sl} (expected 2400.0)")
        
        return True
        
    except Exception as e:
        print(f"✗ TextProcessor test failed: {e}")
        return False


def test_message_parser():
    """Test message parsing."""
    print("\nTesting MessageParser...")
    
    try:
        parser = MessageParser("data/stock_list.csv", fuzzy_threshold=80, max_targets=3)
        
        # Test messages
        test_messages = [
            {
                'timestamp': datetime.now(),
                'channel': 'test_channel',
                'message_id': 123,
                'raw_message': 'RELIANCE BUY at 2500, Target: 2600, 2700, SL: 2400'
            },
            {
                'timestamp': datetime.now(),
                'channel': 'test_channel',
                'message_id': 124,
                'raw_message': 'TCS 3200 CE Entry: 45 Target: 55, 65 SL: 35'
            },
            {
                'timestamp': datetime.now(),
                'channel': 'test_channel',
                'message_id': 125,
                'raw_message': 'Random message without trading info'
            }
        ]
        
        parsed_count = 0
        for msg in test_messages:
            result = parser.parse_message(msg)
            if result:
                print(f"✓ Parsed: {result['stock_symbol']} - {result['direction']}")
                parsed_count += 1
            else:
                print(f"✗ Failed to parse: {msg['raw_message'][:50]}...")
        
        stats = parser.get_statistics()
        print(f"Parser statistics: {stats}")
        
        return parsed_count >= 2  # Should parse at least 2 out of 3 messages
        
    except Exception as e:
        print(f"✗ MessageParser test failed: {e}")
        return False


def test_csv_logger():
    """Test CSV logging functionality."""
    print("\nTesting CSVLogger...")
    
    try:
        # Create temporary files
        with tempfile.TemporaryDirectory() as temp_dir:
            raw_csv = os.path.join(temp_dir, "raw_test.csv")
            enriched_csv = os.path.join(temp_dir, "enriched_test.csv")
            
            logger = CSVLogger(raw_csv, enriched_csv)
            
            # Test raw message logging
            logger.log_raw_message(
                datetime.now(),
                "test_channel",
                123,
                "Test message"
            )
            
            # Test enriched message logging
            enriched_data = {
                'timestamp': datetime.now(),
                'channel': 'test_channel',
                'message_id': 124,
                'raw_message': 'RELIANCE BUY at 2500',
                'stock_symbol': 'RELIANCE',
                'instrument_type': 'Equity',
                'direction': 'Buy',
                'entry_price': 2500.0,
                'stop_loss': 2400.0,
                'target1': 2600.0,
                'target2': 2700.0,
                'target3': None,
                'strike_price': None,
                'option_type': '',
                'expiry_date': '',
                'timeframe': ''
            }
            
            logger.log_enriched_message(enriched_data)
            
            # Verify files were created and have content
            if os.path.exists(raw_csv) and os.path.exists(enriched_csv):
                with open(raw_csv, 'r') as f:
                    raw_lines = len(f.readlines())
                with open(enriched_csv, 'r') as f:
                    enriched_lines = len(f.readlines())
                
                if raw_lines >= 2 and enriched_lines >= 2:  # Header + data
                    print("✓ CSV logging successful")
                    print(f"  - Raw CSV: {raw_lines} lines")
                    print(f"  - Enriched CSV: {enriched_lines} lines")
                    return True
                else:
                    print(f"✗ CSV files don't have expected content")
                    return False
            else:
                print("✗ CSV files were not created")
                return False
                
    except Exception as e:
        print(f"✗ CSVLogger test failed: {e}")
        return False


def test_utility_functions():
    """Test utility functions."""
    print("\nTesting utility functions...")
    
    try:
        # Test clean_message_text
        dirty_text = "  RELIANCE   BUY  at  2500  !!!  "
        clean_text = clean_message_text(dirty_text)
        expected = "RELIANCE BUY at 2500"
        if clean_text.strip() == expected:
            print(f"✓ Text cleaning: '{dirty_text}' -> '{clean_text}'")
        else:
            print(f"✗ Text cleaning failed: '{clean_text}' (expected '{expected}')")
        
        # Test parse_timeframe
        timeframe_tests = [
            ("intraday trade", "intraday"),
            ("5 minutes", "5 minutes"),
            ("swing trading", "swing"),
            ("no timeframe here", None),
        ]
        
        for text, expected in timeframe_tests:
            result = parse_timeframe(text)
            if result == expected:
                print(f"✓ Timeframe parsing: '{text}' -> {result}")
            else:
                print(f"✗ Timeframe parsing: '{text}' -> {result} (expected {expected})")
        
        return True
        
    except Exception as e:
        print(f"✗ Utility functions test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("=" * 60)
    print("TELEGRAM TIP EXTRACTOR - COMPONENT TESTS")
    print("=" * 60)
    
    tests = [
        test_config_loader,
        test_stock_matcher,
        test_text_processor,
        test_message_parser,
        test_csv_logger,
        test_utility_functions,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test_func.__name__} crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All tests passed! The application should work correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the configuration and dependencies.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
