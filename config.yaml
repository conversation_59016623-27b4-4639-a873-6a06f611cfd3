# Telegram API Configuration
telegram:
  api_id: 24368626  # Get from https://my.telegram.org
  api_hash: "ff0729aec7968804345f4e8ab10b99a1"  # Get from https://my.telegram.org
  session_name: "telegram_tip_extractor"  # Session file name
  
# Channels to monitor (can be usernames or IDs)
channels:
  - "Channel TEST for ScorePandit"
  # - "@one_call_new"           # @ONE_CALL💎 - 25,477 members
  # - "@safetraders90"          # Chart Studies with Safe Traders - 7,336 members
  # - "@rahuloptions"           # DAILY TRADING STRATEGIES - 6,479 members
  # - "@daytraderstoption"      # DAY TRADER - Educational Purpose - 22,984 members

# File paths
files:
  raw_csv_log: "data/raw_messages.csv"
  enriched_csv_log: "data/enriched_messages.csv"
  stock_list: "data/stock_list.csv"

# ClickHouse Configuration
clickhouse:
  host: "localhost"
  port: 8123
  user: "scorepandit"
  password: "Score@1615"
  database: "telegram"
  table: "enriched_messages"

# Parsing Configuration
parsing:
  fuzzy_match_threshold: 80  # Minimum similarity score for stock name matching
  max_targets: 3  # Maximum number of targets to extract
  
# Logging Configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file: "logs/app.log"
