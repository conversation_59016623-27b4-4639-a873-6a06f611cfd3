-- ClickHouse database and table creation script
-- Run this manually if you want to create the database and table beforehand

-- Create database
CREATE DATABASE IF NOT EXISTS trading_tips;

-- Use the database
USE trading_tips;

-- Create the enriched_messages table
CREATE TABLE IF NOT EXISTS enriched_messages (
    timestamp DateTime,
    channel String,
    message_id UInt64,
    raw_message String,
    stock_symbol String,
    instrument_type String,
    strike_price Nullable(Float64),
    option_type String,
    expiry_date String,
    direction String,
    entry_price Nullable(Float64),
    stop_loss Nullable(Float64),
    target1 Nullable(Float64),
    target2 Nullable(Float64),
    target3 Nullable(Float64),
    timeframe String,
    created_at DateTime DEFAULT now()
) ENGINE = MergeTree()
ORDER BY (timestamp, channel, message_id);

-- Create indexes for better query performance
-- ALTER TABLE enriched_messages ADD INDEX idx_stock_symbol stock_symbol TYPE bloom_filter GRANULARITY 1;
-- ALTER TABLE enriched_messages ADD INDEX idx_channel channel TYPE bloom_filter GRANULARITY 1;
-- ALTER TABLE enriched_messages ADD INDEX idx_instrument_type instrument_type TYPE bloom_filter GRANULARITY 1;

-- Example queries:

-- Get all tips for a specific stock
-- SELECT * FROM enriched_messages WHERE stock_symbol = 'RELIANCE' ORDER BY timestamp DESC;

-- Get tips from last 24 hours
-- SELECT * FROM enriched_messages WHERE timestamp >= now() - INTERVAL 1 DAY ORDER BY timestamp DESC;

-- Get statistics by channel
-- SELECT channel, count() as message_count, uniq(stock_symbol) as unique_stocks 
-- FROM enriched_messages 
-- GROUP BY channel 
-- ORDER BY message_count DESC;

-- Get most mentioned stocks
-- SELECT stock_symbol, count() as mentions 
-- FROM enriched_messages 
-- GROUP BY stock_symbol 
-- ORDER BY mentions DESC 
-- LIMIT 10;
