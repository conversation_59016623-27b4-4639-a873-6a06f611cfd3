"""
Example of how to create custom parsers for specific channel formats.
This shows how to extend the base parser for channels with unique message formats.
"""

from datetime import datetime
from typing import Dict, Any, Optional
import re
from parser import MessageParser, ChannelSpecificParser


def parse_structured_channel(message_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Parser for channels that use a structured format like:
    
    📈 STOCK: RELIANCE
    🎯 ACTION: BUY
    💰 PRICE: 2500
    🎯 TARGETS: 2600 | 2700 | 2800
    🛑 STOP LOSS: 2400
    ⏰ TIMEFRAME: Intraday
    """
    try:
        text = message_data.get('raw_message', '')
        
        # Initialize result with basic fields
        result = {
            'timestamp': message_data.get('timestamp', datetime.now()),
            'channel': message_data.get('channel', ''),
            'message_id': message_data.get('message_id', 0),
            'raw_message': text,
            'instrument_type': 'Equity',
            'option_type': '',
            'strike_price': None,
            'expiry_date': '',
            'target1': None,
            'target2': None,
            'target3': None,
        }
        
        # Extract stock symbol
        stock_match = re.search(r'STOCK:\s*([A-Z]+)', text, re.IGNORECASE)
        if stock_match:
            result['stock_symbol'] = stock_match.group(1).upper()
        else:
            return None
        
        # Extract action/direction
        action_match = re.search(r'ACTION:\s*(BUY|SELL|LONG|SHORT)', text, re.IGNORECASE)
        if action_match:
            action = action_match.group(1).upper()
            result['direction'] = 'Buy' if action in ['BUY', 'LONG'] else 'Sell'
        
        # Extract price
        price_match = re.search(r'PRICE:\s*(\d+(?:\.\d+)?)', text, re.IGNORECASE)
        if price_match:
            result['entry_price'] = float(price_match.group(1))
        
        # Extract targets
        targets_match = re.search(r'TARGETS:\s*([\d\s\|\.\,]+)', text, re.IGNORECASE)
        if targets_match:
            targets_text = targets_match.group(1)
            # Split by | or , and extract numbers
            target_numbers = re.findall(r'\d+(?:\.\d+)?', targets_text)
            for i, target in enumerate(target_numbers[:3]):
                result[f'target{i+1}'] = float(target)
        
        # Extract stop loss
        sl_match = re.search(r'STOP\s*LOSS:\s*(\d+(?:\.\d+)?)', text, re.IGNORECASE)
        if sl_match:
            result['stop_loss'] = float(sl_match.group(1))
        
        # Extract timeframe
        timeframe_match = re.search(r'TIMEFRAME:\s*(\w+)', text, re.IGNORECASE)
        if timeframe_match:
            result['timeframe'] = timeframe_match.group(1)
        
        return result
        
    except Exception as e:
        print(f"Error in structured channel parser: {e}")
        return None


def parse_options_channel(message_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Parser for options-focused channels with format like:
    
    NIFTY 18000 CE @ 150
    Target: 180, 200
    SL: 120
    Expiry: 25 Jan
    """
    try:
        text = message_data.get('raw_message', '')
        
        # Initialize result
        result = {
            'timestamp': message_data.get('timestamp', datetime.now()),
            'channel': message_data.get('channel', ''),
            'message_id': message_data.get('message_id', 0),
            'raw_message': text,
            'instrument_type': 'Options',
            'direction': 'Buy',  # Default for options
            'expiry_date': '',
            'target1': None,
            'target2': None,
            'target3': None,
        }
        
        # Extract stock and option details (e.g., "NIFTY 18000 CE")
        option_match = re.search(r'([A-Z]+)\s+(\d+)\s+(CE|PE)', text, re.IGNORECASE)
        if option_match:
            result['stock_symbol'] = option_match.group(1).upper()
            result['strike_price'] = float(option_match.group(2))
            result['option_type'] = option_match.group(3).upper()
        else:
            return None
        
        # Extract entry price (after @)
        price_match = re.search(r'@\s*(\d+(?:\.\d+)?)', text)
        if price_match:
            result['entry_price'] = float(price_match.group(1))
        
        # Extract targets
        targets_match = re.search(r'Target:\s*([\d\s\,\.]+)', text, re.IGNORECASE)
        if targets_match:
            targets_text = targets_match.group(1)
            target_numbers = re.findall(r'\d+(?:\.\d+)?', targets_text)
            for i, target in enumerate(target_numbers[:3]):
                result[f'target{i+1}'] = float(target)
        
        # Extract stop loss
        sl_match = re.search(r'SL:\s*(\d+(?:\.\d+)?)', text, re.IGNORECASE)
        if sl_match:
            result['stop_loss'] = float(sl_match.group(1))
        
        # Extract expiry
        expiry_match = re.search(r'Expiry:\s*([^\n]+)', text, re.IGNORECASE)
        if expiry_match:
            result['expiry_date'] = expiry_match.group(1).strip()
        
        return result
        
    except Exception as e:
        print(f"Error in options channel parser: {e}")
        return None


def parse_futures_channel(message_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Parser for futures trading channels with format like:
    
    BANKNIFTY FUT LONG 45000
    TGT: 45500, 46000
    SL: 44500
    """
    try:
        text = message_data.get('raw_message', '')
        
        # Initialize result
        result = {
            'timestamp': message_data.get('timestamp', datetime.now()),
            'channel': message_data.get('channel', ''),
            'message_id': message_data.get('message_id', 0),
            'raw_message': text,
            'instrument_type': 'Futures',
            'option_type': '',
            'strike_price': None,
            'expiry_date': '',
            'target1': None,
            'target2': None,
            'target3': None,
        }
        
        # Extract stock, direction and price (e.g., "BANKNIFTY FUT LONG 45000")
        futures_match = re.search(r'([A-Z]+)\s+FUT\s+(LONG|SHORT)\s+(\d+(?:\.\d+)?)', text, re.IGNORECASE)
        if futures_match:
            result['stock_symbol'] = futures_match.group(1).upper()
            direction = futures_match.group(2).upper()
            result['direction'] = 'Buy' if direction == 'LONG' else 'Sell'
            result['entry_price'] = float(futures_match.group(3))
        else:
            return None
        
        # Extract targets
        targets_match = re.search(r'TGT:\s*([\d\s\,\.]+)', text, re.IGNORECASE)
        if targets_match:
            targets_text = targets_match.group(1)
            target_numbers = re.findall(r'\d+(?:\.\d+)?', targets_text)
            for i, target in enumerate(target_numbers[:3]):
                result[f'target{i+1}'] = float(target)
        
        # Extract stop loss
        sl_match = re.search(r'SL:\s*(\d+(?:\.\d+)?)', text, re.IGNORECASE)
        if sl_match:
            result['stop_loss'] = float(sl_match.group(1))
        
        return result
        
    except Exception as e:
        print(f"Error in futures channel parser: {e}")
        return None


# Example of how to use custom parsers in your main application
def setup_custom_parsers(base_parser: MessageParser) -> ChannelSpecificParser:
    """
    Setup custom parsers for specific channels.
    
    Args:
        base_parser: The base MessageParser instance
        
    Returns:
        ChannelSpecificParser with custom rules added
    """
    channel_parser = ChannelSpecificParser(base_parser)
    
    # Add custom parsers for specific channels
    channel_parser.add_channel_rule("Structured Trading Tips", parse_structured_channel)
    channel_parser.add_channel_rule("Options Expert", parse_options_channel)
    channel_parser.add_channel_rule("Futures Guru", parse_futures_channel)
    
    return channel_parser


if __name__ == "__main__":
    # Test the custom parsers
    
    # Test structured channel parser
    test_message_1 = {
        'timestamp': datetime.now(),
        'channel': 'Structured Trading Tips',
        'message_id': 123,
        'raw_message': '''📈 STOCK: RELIANCE
🎯 ACTION: BUY
💰 PRICE: 2500
🎯 TARGETS: 2600 | 2700 | 2800
🛑 STOP LOSS: 2400
⏰ TIMEFRAME: Intraday'''
    }
    
    result_1 = parse_structured_channel(test_message_1)
    print("Structured channel result:")
    print(result_1)
    print()
    
    # Test options channel parser
    test_message_2 = {
        'timestamp': datetime.now(),
        'channel': 'Options Expert',
        'message_id': 124,
        'raw_message': '''NIFTY 18000 CE @ 150
Target: 180, 200
SL: 120
Expiry: 25 Jan'''
    }
    
    result_2 = parse_options_channel(test_message_2)
    print("Options channel result:")
    print(result_2)
    print()
    
    # Test futures channel parser
    test_message_3 = {
        'timestamp': datetime.now(),
        'channel': 'Futures Guru',
        'message_id': 125,
        'raw_message': '''BANKNIFTY FUT LONG 45000
TGT: 45500, 46000
SL: 44500'''
    }
    
    result_3 = parse_futures_channel(test_message_3)
    print("Futures channel result:")
    print(result_3)
