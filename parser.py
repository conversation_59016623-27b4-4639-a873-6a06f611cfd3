"""
Message parser for Telegram Tip Extractor.
Handles parsing and enrichment of trading messages.
"""

import logging
import re
from datetime import datetime
from typing import Dict, Any, Optional, List
from utils import StockMatcher, TextProcessor, clean_message_text, parse_timeframe


logger = logging.getLogger(__name__)


class MessageParser:
    """Parses and enriches trading messages from Telegram channels."""
    
    def __init__(self, stock_list_path: str, fuzzy_threshold: int = 80, max_targets: int = 3):
        """
        Initialize the message parser.
        
        Args:
            stock_list_path: Path to stock list CSV file
            fuzzy_threshold: Minimum similarity score for fuzzy matching
            max_targets: Maximum number of targets to extract
        """
        self.stock_matcher = StockMatcher(stock_list_path, fuzzy_threshold)
        self.text_processor = TextProcessor()
        self.max_targets = max_targets
        
        # Statistics
        self.total_messages = 0
        self.parsed_messages = 0
        self.failed_messages = 0
    
    def parse_message(self, message_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Parse and enrich a single message.
        
        Args:
            message_data: Raw message data from Telegram
            
        Returns:
            Enriched message data or None if parsing failed
        """
        self.total_messages += 1
        
        try:
            raw_message = message_data.get('raw_message', '')
            
            # Skip empty messages
            if not raw_message or len(raw_message.strip()) < 10:
                logger.debug("Skipping empty or too short message")
                return None
            
            # Clean the message text
            cleaned_text = clean_message_text(raw_message)
            
            # Check if this looks like a trading message
            if not self._is_trading_message(cleaned_text):
                logger.debug("Message doesn't appear to be a trading tip")
                return None
            
            # Extract stock symbol
            stock_symbol = self.stock_matcher.find_stock_symbol(cleaned_text)
            if not stock_symbol:
                logger.debug("No valid stock symbol found in message")
                return None
            
            # Extract trading information
            enriched_data = {
                'timestamp': message_data.get('timestamp', datetime.now()),
                'channel': message_data.get('channel', ''),
                'message_id': message_data.get('message_id', 0),
                'raw_message': raw_message,
                'stock_symbol': stock_symbol,
                'instrument_type': self.text_processor.determine_instrument_type(cleaned_text),
                'direction': self.text_processor.extract_direction(cleaned_text),
                'entry_price': self.text_processor.extract_price(cleaned_text),
                'stop_loss': self.text_processor.extract_stop_loss(cleaned_text),
                'timeframe': parse_timeframe(cleaned_text)
            }
            
            # Extract targets
            targets = self.text_processor.extract_targets(cleaned_text, self.max_targets)
            for i in range(self.max_targets):
                target_key = f'target{i+1}'
                enriched_data[target_key] = targets[i] if i < len(targets) else None
            
            # Extract option-specific information
            if enriched_data['instrument_type'] == 'Options':
                option_type, strike_price, expiry_date = self.text_processor.extract_option_info(cleaned_text)
                enriched_data['option_type'] = option_type
                enriched_data['strike_price'] = strike_price
                enriched_data['expiry_date'] = expiry_date
            else:
                enriched_data['option_type'] = ''
                enriched_data['strike_price'] = None
                enriched_data['expiry_date'] = ''
            
            # Validate the enriched data
            if self._validate_enriched_data(enriched_data):
                self.parsed_messages += 1
                logger.debug(f"Successfully parsed message: {stock_symbol}")
                return enriched_data
            else:
                logger.debug("Enriched data validation failed")
                return None
                
        except Exception as e:
            self.failed_messages += 1
            logger.error(f"Error parsing message: {e}")
            return None
    
    def _is_trading_message(self, text: str) -> bool:
        """
        Check if the message appears to be a trading tip.
        
        Args:
            text: Cleaned message text
            
        Returns:
            True if message appears to be a trading tip
        """
        text_lower = text.lower()
        
        # Look for trading keywords
        trading_keywords = [
            'buy', 'sell', 'long', 'short', 'call', 'put',
            'target', 'tgt', 'stop loss', 'sl', 'entry',
            'price', 'support', 'resistance', 'breakout',
            'ce', 'pe', 'futures', 'options', 'equity'
        ]
        
        # Check if message contains trading keywords
        keyword_count = sum(1 for keyword in trading_keywords if keyword in text_lower)
        
        # Look for price patterns
        price_patterns = [
            r'\b\d+(?:\.\d+)?\b',  # Numbers that could be prices
            r'rs\.?\s*\d+',        # Rs. 100 format
            r'₹\s*\d+',            # Rupee symbol
        ]
        
        has_price = any(re.search(pattern, text_lower) for pattern in price_patterns)
        
        # Message should have at least 2 trading keywords or 1 keyword + price
        return keyword_count >= 2 or (keyword_count >= 1 and has_price)
    
    def _validate_enriched_data(self, data: Dict[str, Any]) -> bool:
        """
        Validate enriched message data.
        
        Args:
            data: Enriched message data
            
        Returns:
            True if data is valid
        """
        # Must have stock symbol
        if not data.get('stock_symbol'):
            return False
        
        # Must have at least one of: direction, entry_price, targets, or stop_loss
        has_trading_info = any([
            data.get('direction'),
            data.get('entry_price'),
            data.get('target1'),
            data.get('stop_loss')
        ])
        
        return has_trading_info
    
    def get_statistics(self) -> Dict[str, int]:
        """
        Get parsing statistics.
        
        Returns:
            Dictionary with parsing statistics
        """
        return {
            'total_messages': self.total_messages,
            'parsed_messages': self.parsed_messages,
            'failed_messages': self.failed_messages,
            'success_rate': round((self.parsed_messages / max(self.total_messages, 1)) * 100, 2)
        }
    
    def reset_statistics(self) -> None:
        """Reset parsing statistics."""
        self.total_messages = 0
        self.parsed_messages = 0
        self.failed_messages = 0


class ChannelSpecificParser:
    """Handles channel-specific parsing rules."""
    
    def __init__(self, base_parser: MessageParser):
        """
        Initialize channel-specific parser.
        
        Args:
            base_parser: Base message parser instance
        """
        self.base_parser = base_parser
        self.channel_rules = {}
    
    def add_channel_rule(self, channel_name: str, rule_function: callable) -> None:
        """
        Add a channel-specific parsing rule.
        
        Args:
            channel_name: Name of the channel
            rule_function: Function that takes message text and returns parsed data
        """
        self.channel_rules[channel_name] = rule_function
        logger.info(f"Added custom rule for channel: {channel_name}")
    
    def parse_message(self, message_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Parse message using channel-specific rules if available.
        
        Args:
            message_data: Raw message data from Telegram
            
        Returns:
            Enriched message data or None if parsing failed
        """
        channel_name = message_data.get('channel', '')
        
        # Try channel-specific rule first
        if channel_name in self.channel_rules:
            try:
                custom_result = self.channel_rules[channel_name](message_data)
                if custom_result:
                    logger.debug(f"Used custom rule for channel: {channel_name}")
                    return custom_result
            except Exception as e:
                logger.error(f"Error in custom rule for {channel_name}: {e}")
        
        # Fall back to base parser
        return self.base_parser.parse_message(message_data)


# Example channel-specific parsing functions
def parse_premium_channel_format(message_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Example parser for a premium channel with specific format.
    
    Expected format:
    STOCK: RELIANCE
    ACTION: BUY
    PRICE: 2500
    TARGET: 2600, 2700
    SL: 2400
    """
    try:
        text = message_data.get('raw_message', '')
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        
        parsed_data = {
            'timestamp': message_data.get('timestamp', datetime.now()),
            'channel': message_data.get('channel', ''),
            'message_id': message_data.get('message_id', 0),
            'raw_message': text,
        }
        
        for line in lines:
            if line.upper().startswith('STOCK:'):
                parsed_data['stock_symbol'] = line.split(':', 1)[1].strip().upper()
            elif line.upper().startswith('ACTION:'):
                action = line.split(':', 1)[1].strip().upper()
                parsed_data['direction'] = 'Buy' if 'BUY' in action else 'Sell'
            elif line.upper().startswith('PRICE:'):
                try:
                    parsed_data['entry_price'] = float(line.split(':', 1)[1].strip())
                except ValueError:
                    pass
            elif line.upper().startswith('TARGET:'):
                targets_text = line.split(':', 1)[1].strip()
                targets = re.findall(r'\d+(?:\.\d+)?', targets_text)
                for i, target in enumerate(targets[:3]):
                    parsed_data[f'target{i+1}'] = float(target)
            elif line.upper().startswith('SL:'):
                try:
                    parsed_data['stop_loss'] = float(line.split(':', 1)[1].strip())
                except ValueError:
                    pass
        
        # Set defaults
        parsed_data.setdefault('instrument_type', 'Equity')
        parsed_data.setdefault('option_type', '')
        parsed_data.setdefault('strike_price', None)
        parsed_data.setdefault('expiry_date', '')
        parsed_data.setdefault('timeframe', '')
        
        # Fill missing targets with None
        for i in range(1, 4):
            parsed_data.setdefault(f'target{i}', None)
        
        return parsed_data if parsed_data.get('stock_symbol') else None
        
    except Exception as e:
        logger.error(f"Error in premium channel parser: {e}")
        return None


if __name__ == "__main__":
    # Test the parser
    parser = MessageParser("data/stock_list.csv")
    
    # Test message
    test_message = {
        'timestamp': datetime.now(),
        'channel': 'test_channel',
        'message_id': 123,
        'raw_message': 'RELIANCE BUY at 2500, Target: 2600, 2700, SL: 2400'
    }
    
    result = parser.parse_message(test_message)
    if result:
        print("Parsing successful!")
        for key, value in result.items():
            print(f"{key}: {value}")
    else:
        print("Parsing failed")
    
    print(f"Statistics: {parser.get_statistics()}")
