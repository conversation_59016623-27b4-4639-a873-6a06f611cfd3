"""
Configuration loader for Telegram Tip Extractor.
Handles loading and validation of YAML configuration files.
"""

import yaml
import os
import logging
from typing import Dict, Any, List
from pathlib import Path


class ConfigLoader:
    """Loads and validates configuration from YAML file."""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        Initialize the config loader.
        
        Args:
            config_path: Path to the YAML configuration file
        """
        self.config_path = config_path
        self.config = None
        
    def load_config(self) -> Dict[str, Any]:
        """
        Load configuration from YAML file.
        
        Returns:
            Dictionary containing configuration data
            
        Raises:
            FileNotFoundError: If config file doesn't exist
            yaml.YAMLError: If config file is invalid YAML
            ValueError: If required configuration is missing
        """
        if not os.path.exists(self.config_path):
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
            
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                self.config = yaml.safe_load(file)
        except yaml.YAMLError as e:
            raise yaml.YAMLError(f"Invalid YAML in config file: {e}")
            
        self._validate_config()
        self._create_directories()
        
        return self.config
    
    def _validate_config(self) -> None:
        """
        Validate that all required configuration sections exist.
        
        Raises:
            ValueError: If required configuration is missing
        """
        required_sections = ['telegram', 'channels', 'files', 'clickhouse', 'parsing']
        
        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"Missing required configuration section: {section}")
        
        # Validate telegram section
        telegram_config = self.config['telegram']
        required_telegram_fields = ['api_id', 'api_hash', 'session_name']
        for field in required_telegram_fields:
            if field not in telegram_config:
                raise ValueError(f"Missing required telegram field: {field}")
        
        # Validate channels
        if not isinstance(self.config['channels'], list) or len(self.config['channels']) == 0:
            raise ValueError("At least one channel must be configured")
        
        # Validate file paths
        files_config = self.config['files']
        required_file_fields = ['raw_csv_log', 'enriched_csv_log', 'stock_list']
        for field in required_file_fields:
            if field not in files_config:
                raise ValueError(f"Missing required file path: {field}")
        
        # Validate ClickHouse config
        clickhouse_config = self.config['clickhouse']
        required_ch_fields = ['host', 'port', 'database', 'table']
        for field in required_ch_fields:
            if field not in clickhouse_config:
                raise ValueError(f"Missing required ClickHouse field: {field}")
    
    def _create_directories(self) -> None:
        """Create necessary directories for log files and data."""
        # Create directories for file paths
        for file_key in ['raw_csv_log', 'enriched_csv_log']:
            file_path = self.config['files'][file_key]
            directory = os.path.dirname(file_path)
            if directory:
                Path(directory).mkdir(parents=True, exist_ok=True)
        
        # Create logs directory if logging file is specified
        if 'logging' in self.config and 'file' in self.config['logging']:
            log_file = self.config['logging']['file']
            log_directory = os.path.dirname(log_file)
            if log_directory:
                Path(log_directory).mkdir(parents=True, exist_ok=True)
    
    def get_telegram_config(self) -> Dict[str, Any]:
        """Get Telegram API configuration."""
        return self.config['telegram']
    
    def get_channels(self) -> List[str]:
        """Get list of channels to monitor."""
        return self.config['channels']
    
    def get_file_paths(self) -> Dict[str, str]:
        """Get file path configuration."""
        return self.config['files']
    
    def get_clickhouse_config(self) -> Dict[str, Any]:
        """Get ClickHouse configuration."""
        return self.config['clickhouse']
    
    def get_parsing_config(self) -> Dict[str, Any]:
        """Get parsing configuration."""
        return self.config['parsing']
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging configuration."""
        return self.config.get('logging', {'level': 'INFO'})


def setup_logging(config: Dict[str, Any]) -> None:
    """
    Setup logging configuration.
    
    Args:
        config: Logging configuration dictionary
    """
    log_level = getattr(logging, config.get('level', 'INFO').upper())
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # Configure logging
    logging.basicConfig(
        level=log_level,
        format=log_format,
        handlers=[
            logging.StreamHandler(),  # Console output
        ]
    )
    
    # Add file handler if log file is specified
    if 'file' in config:
        file_handler = logging.FileHandler(config['file'])
        file_handler.setFormatter(logging.Formatter(log_format))
        logging.getLogger().addHandler(file_handler)


if __name__ == "__main__":
    # Test the config loader
    try:
        loader = ConfigLoader()
        config = loader.load_config()
        print("Configuration loaded successfully!")
        print(f"Monitoring {len(loader.get_channels())} channels")
        print(f"Stock list file: {loader.get_file_paths()['stock_list']}")
    except Exception as e:
        print(f"Error loading configuration: {e}")
