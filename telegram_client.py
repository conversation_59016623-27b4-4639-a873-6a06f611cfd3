"""
Telegram client for Telegram Tip Extractor.
Handles connection to Telegram API and message fetching using Telethon.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Callable, Optional
from telethon import TelegramClient, events
from telethon.tl.types import Channel, Chat, User
from telethon.errors import SessionPasswordNeededError, FloodWaitError
import time


logger = logging.getLogger(__name__)


class TelegramTipClient:
    """Handles Telegram API operations for fetching messages."""
    
    def __init__(self, api_id: int, api_hash: str, session_name: str):
        """
        Initialize Telegram client.
        
        Args:
            api_id: Telegram API ID
            api_hash: Telegram API hash
            session_name: Session file name
        """
        self.api_id = api_id
        self.api_hash = api_hash
        self.session_name = session_name
        self.client = None
        self.monitored_channels = []
        self.message_handler = None
        self.is_running = False
    
    async def connect(self) -> bool:
        """
        Connect to Telegram API.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            self.client = TelegramClient(self.session_name, self.api_id, self.api_hash)
            await self.client.start()
            
            if not await self.client.is_user_authorized():
                logger.error("User not authorized. Please run the client interactively first.")
                return False
            
            me = await self.client.get_me()
            logger.info(f"Connected to Telegram as {me.first_name} (@{me.username})")
            return True
            
        except SessionPasswordNeededError:
            logger.error("Two-factor authentication enabled. Please run interactively first.")
            return False
        except Exception as e:
            logger.error(f"Failed to connect to Telegram: {e}")
            return False
    
    async def setup_channels(self, channel_list: List[str]) -> bool:
        """
        Setup channels to monitor.
        
        Args:
            channel_list: List of channel usernames or IDs
            
        Returns:
            True if setup successful, False otherwise
        """
        if not self.client:
            logger.error("Client not connected")
            return False
        
        self.monitored_channels = []
        
        for channel_identifier in channel_list:
            try:
                # Get channel entity
                entity = await self.client.get_entity(channel_identifier)
                
                # Check if we can access the channel
                if isinstance(entity, (Channel, Chat)):
                    self.monitored_channels.append({
                        'entity': entity,
                        'identifier': channel_identifier,
                        'title': getattr(entity, 'title', channel_identifier),
                        'id': entity.id
                    })
                    logger.info(f"Added channel: {entity.title} ({channel_identifier})")
                else:
                    logger.warning(f"Skipping non-channel entity: {channel_identifier}")
                    
            except Exception as e:
                logger.error(f"Failed to add channel {channel_identifier}: {e}")
        
        if not self.monitored_channels:
            logger.error("No channels successfully added")
            return False
        
        logger.info(f"Successfully setup {len(self.monitored_channels)} channels")
        return True
    
    def set_message_handler(self, handler: Callable[[Dict[str, Any]], None]) -> None:
        """
        Set the message handler function.
        
        Args:
            handler: Function to call when a new message is received
        """
        self.message_handler = handler
    
    async def start_monitoring(self) -> None:
        """Start monitoring channels for new messages."""
        if not self.client or not self.monitored_channels:
            logger.error("Client not connected or no channels setup")
            return
        
        if not self.message_handler:
            logger.error("No message handler set")
            return
        
        # Register event handler for new messages
        @self.client.on(events.NewMessage)
        async def handle_new_message(event):
            try:
                # Check if message is from a monitored channel
                channel_info = None
                for channel in self.monitored_channels:
                    if event.chat_id == channel['id']:
                        channel_info = channel
                        break
                
                if not channel_info:
                    return  # Message not from monitored channel
                
                # Extract message data
                message_data = {
                    'timestamp': event.date or datetime.now(),
                    'channel': channel_info['title'],
                    'channel_id': channel_info['id'],
                    'message_id': event.id,
                    'raw_message': event.text or '',
                    'sender_id': event.sender_id,
                    'is_reply': event.is_reply,
                    'reply_to_msg_id': event.reply_to_msg_id if event.is_reply else None
                }
                
                # Call the message handler
                if self.message_handler:
                    self.message_handler(message_data)
                
                logger.debug(f"Processed message from {channel_info['title']}: {event.id}")
                
            except Exception as e:
                logger.error(f"Error handling new message: {e}")
        
        self.is_running = True
        logger.info("Started monitoring channels for new messages")
        
        try:
            # Keep the client running
            await self.client.run_until_disconnected()
        except KeyboardInterrupt:
            logger.info("Monitoring stopped by user")
        except Exception as e:
            logger.error(f"Error during monitoring: {e}")
        finally:
            self.is_running = False
    
    async def fetch_recent_messages(self, hours: int = 24, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Fetch recent messages from monitored channels.
        
        Args:
            hours: Number of hours to look back
            limit: Maximum number of messages per channel
            
        Returns:
            List of message dictionaries
        """
        if not self.client or not self.monitored_channels:
            logger.error("Client not connected or no channels setup")
            return []
        
        all_messages = []
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        for channel_info in self.monitored_channels:
            try:
                messages = []
                async for message in self.client.iter_messages(
                    channel_info['entity'], 
                    limit=limit,
                    offset_date=cutoff_time
                ):
                    if message.date < cutoff_time:
                        break
                    
                    message_data = {
                        'timestamp': message.date,
                        'channel': channel_info['title'],
                        'channel_id': channel_info['id'],
                        'message_id': message.id,
                        'raw_message': message.text or '',
                        'sender_id': message.sender_id,
                        'is_reply': message.is_reply,
                        'reply_to_msg_id': message.reply_to_msg_id if message.is_reply else None
                    }
                    messages.append(message_data)
                
                all_messages.extend(messages)
                logger.info(f"Fetched {len(messages)} recent messages from {channel_info['title']}")
                
                # Add small delay to avoid rate limiting
                await asyncio.sleep(1)
                
            except FloodWaitError as e:
                logger.warning(f"Rate limited for {e.seconds} seconds on {channel_info['title']}")
                await asyncio.sleep(e.seconds)
            except Exception as e:
                logger.error(f"Error fetching messages from {channel_info['title']}: {e}")
        
        # Sort messages by timestamp
        all_messages.sort(key=lambda x: x['timestamp'], reverse=True)
        logger.info(f"Fetched total {len(all_messages)} recent messages")
        
        return all_messages
    
    async def get_channel_info(self) -> List[Dict[str, Any]]:
        """
        Get information about monitored channels.
        
        Returns:
            List of channel information dictionaries
        """
        if not self.client or not self.monitored_channels:
            return []
        
        channel_info_list = []
        
        for channel_info in self.monitored_channels:
            try:
                entity = channel_info['entity']
                
                # Get participant count if possible
                participants_count = None
                try:
                    if hasattr(entity, 'participants_count'):
                        participants_count = entity.participants_count
                except:
                    pass
                
                info = {
                    'title': channel_info['title'],
                    'identifier': channel_info['identifier'],
                    'id': channel_info['id'],
                    'type': 'Channel' if isinstance(entity, Channel) else 'Chat',
                    'participants_count': participants_count,
                    'username': getattr(entity, 'username', None),
                    'description': getattr(entity, 'about', None)
                }
                
                channel_info_list.append(info)
                
            except Exception as e:
                logger.error(f"Error getting info for channel {channel_info['title']}: {e}")
        
        return channel_info_list
    
    async def disconnect(self) -> None:
        """Disconnect from Telegram."""
        if self.client:
            await self.client.disconnect()
            logger.info("Disconnected from Telegram")
    
    def stop_monitoring(self) -> None:
        """Stop monitoring channels."""
        self.is_running = False
        if self.client:
            self.client.disconnect()


async def interactive_login(api_id: int, api_hash: str, session_name: str) -> bool:
    """
    Perform interactive login to create session file.
    
    Args:
        api_id: Telegram API ID
        api_hash: Telegram API hash
        session_name: Session file name
        
    Returns:
        True if login successful, False otherwise
    """
    try:
        client = TelegramClient(session_name, api_id, api_hash)
        await client.start()
        
        if await client.is_user_authorized():
            me = await client.get_me()
            print(f"Already logged in as {me.first_name} (@{me.username})")
            await client.disconnect()
            return True
        else:
            print("Please complete the login process...")
            await client.disconnect()
            return False
            
    except Exception as e:
        logger.error(f"Interactive login failed: {e}")
        return False


if __name__ == "__main__":
    # Test the Telegram client
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "login":
        # Interactive login mode
        print("Interactive login mode")
        print("Please get your API credentials from https://my.telegram.org")
        
        api_id = input("Enter API ID: ")
        api_hash = input("Enter API Hash: ")
        session_name = input("Enter session name (default: telegram_tip_extractor): ") or "telegram_tip_extractor"
        
        asyncio.run(interactive_login(int(api_id), api_hash, session_name))
    else:
        print("Use 'python telegram_client.py login' for interactive login")
